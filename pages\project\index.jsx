import { useEffect, useState } from "react";
import Head from "next/head";
import {
    Breadcrumb,
    IconButton,
    Input,
    InputGroup,
    Pagination,
    Panel,
    Stack,
    Table,
    Tag,
    Button,
    Modal,
    Form,
    useToaster,
    ButtonGroup,
    Loader,
    InputNumber,
    DatePicker,
    SelectPicker,
    Progress,
    Nav,
    Textarea,
} from "rsuite";
import { Trash as TrashIcon, Reload as ReloadIcon } from "@rsuite/icons";
import ContainerLayout from "@/components/layout/ContainerLayout";
import PlusRoundIcon from "@rsuite/icons/PlusRound";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import EditIcon from "@rsuite/icons/Edit";
import ApiProjectPostgress from "@/pages/api/project_old/api_project_postgress";
import { useRouter } from "next/router";
import { useUser } from "@/context/UserContext";
import withRoleAccess from '@/components/auth/withRoleAccess';
import rolePermissions from '@/utils/rolePermissions';

function MasterdataProject() {
    const { HeaderCell, Cell, Column } = Table;
    const [searchKeyword, setSearchKeyword] = useState("");
    const [sortColumn, setSortColumn] = useState("project_code");
    const [sortType, setSortType] = useState("asc");
    const [limit, setLimit] = useState(10);
    const [page, setPage] = useState(1);
    const router = useRouter();
    const [loading, setLoading] = useState(false);

    const { user, isAuthenticated, loading: userLoading } = useUser();

    const [moduleName, setModuleName] = useState("");
    const [kodeProdukDataState, setKodeProdukDataState] = useState([]);

    const emptyAddKodeProdukForm = {
        project_code: "",
        project_name: "",
        department: "",
        pic: "",
        pic_technical: "",
        est_start_date: null,
        est_finish_date: null,
        act_start_date: null,
        act_finish_date: null,
    };

    const emptyEditKodeProdukForm = {
        id_produk: null,
        project_code: "",
        project_name: "",
        department: "",
        pic: "",
        pic_technical: "",
        est_start_date: null,
        est_finish_date: null,
        act_start_date: null,
        act_finish_date: null,
        progress: 0,
        task_of_development: "",
        next_to_do: "",
    };

    const [showAddModal, setShowAddModal] = useState(false);
    const [showEditModal, setShowEditModal] = useState(false);
    const [addKodeProdukForm, setAddKodeProdukForm] = useState(emptyAddKodeProdukForm);
    const [editKodeProdukForm, setEditKodeProdukForm] = useState(emptyEditKodeProdukForm);
    const [errorsAddForm, setErrorsAddForm] = useState({});
    const [errorsEditForm, setErrorsEditForm] = useState({});
    const [addLoading, setAddLoading] = useState(false);
    const [activeEditTab, setActiveEditTab] = useState('general');

    // Tambahkan fungsi untuk mengecek validasi form
    const isFormValid = () => {
        return (
            addKodeProdukForm.project_code?.trim() &&
            addKodeProdukForm.project_name?.trim() &&
            addKodeProdukForm.department &&
            addKodeProdukForm.pic &&
            addKodeProdukForm.pic_technical?.trim() &&
            addKodeProdukForm.est_start_date &&
            addKodeProdukForm.est_finish_date
        );
    };

    const handleSearch = (value) => {
        setSearchKeyword(value);
        setPage(1);
    };

    const handleChangeLimit = (dataKey) => {
        setPage(1);
        setLimit(dataKey);
    };

    const handleSortColumn = (sortColumn, sortType) => {
        setTimeout(() => {
            setSortColumn(sortColumn);
            setSortType(sortType);
        }, 500);
    };

    const filteredData = kodeProdukDataState.filter((rowData) => {
        const searchFields = ["id_produk", "project_code", "project_name", "department", "pic", "pic_technical", "progress", "est_start_date", "est_finish_date", "actual_start_date", "actual_finish_date", "task_of_development"];

        const matchesSearch = searchFields.some((field) => rowData[field]?.toString().toLowerCase().includes(searchKeyword.toLowerCase()));

        return matchesSearch;
    });

    const getPaginatedData = (filteredData, limit, page) => {
        const start = limit * (page - 1);
        const end = start + limit;
        return filteredData.slice(start, end);
    };

    const getFilteredData = () => {
        if (sortColumn && sortType) {
            return filteredData.sort((a, b) => {
                let x = a[sortColumn];
                let y = b[sortColumn];
                if (typeof x === "string") {
                    x = x.charCodeAt();
                }
                if (typeof y === "string") {
                    y = y.charCodeAt();
                }
                if (sortType === "asc") {
                    return x - y;
                } else {
                    return y - x;
                }
            });
        }
        return filteredData;
    };

    const getProgressColor = (percent) => {
        if (percent < 40) {
            return '#d9534f'; // Merah
        }
        if (percent < 80) {
            return '#f0ad4e'; // Oranye
        }
        return '#337ab7'; // Biru
    };

    const totalRowCount = searchKeyword ? filteredData.length : kodeProdukDataState.length;

    useEffect(() => {
        if (userLoading) return;
        if (!isAuthenticated()) {
            router.push("/login");
            return;
        }
        // if (!user?.menu_link_code?.some((item) => item.includes("KodeProduk/masterdata"))) {
        //     router.push("/dashboard");
        //     return;
        // }
        setModuleName(user.module_name || "");
        HandleGetAllKodeProdukApi();
    }, [userLoading]);

    const HandleGetAllKodeProdukApi = async () => {
        try {
            const res = await ApiProjectPostgress().getAllProjectPostgress();

            console.log("res", res);
            if (res.status === 200) {
                setKodeProdukDataState(res.data);
            } else {
                console.log("error on GetAllApi ", res.message);
            }
        } catch (error) {
            console.log("error on catch GetAllApi", error);
        }
    };

    const HandleAddProject = async () => {
        const errors = {};

        if (!addKodeProdukForm.project_code || addKodeProdukForm.project_code.trim() === "") {
            errors.project_code = "Kode Project wajib diisi";
        }
        if (!addKodeProdukForm.project_name || addKodeProdukForm.project_name.trim() === "") {
            errors.project_name = "Nama Project wajib diisi";
        }
        if (!addKodeProdukForm.department) {
            errors.department = "Department wajib dipilih";
        }

        if (!addKodeProdukForm.pic) {
            errors.pic = "PIC wajib diisi";
        }
        if (!addKodeProdukForm.pic_technical || addKodeProdukForm.pic_technical.trim() === "") {
            errors.pic_technical = "PIC Technical wajib diisi";
        }
        if (!addKodeProdukForm.est_start_date) {
            errors.est_start_date = "Estimasi tanggal mulai wajib diisi";
        }
        if (!addKodeProdukForm.est_finish_date) {
            errors.est_finish_date = "Estimasi tanggal selesai wajib diisi";
        }

        if (Object.keys(errors).length > 0) {
            setErrorsAddForm(errors);
            return;
        }

        setAddLoading(true);
        try {
            const res = await ApiProjectPostgress().createProjectPostgress(addKodeProdukForm);

            if (res.status === 200) {
                setAddKodeProdukForm(emptyAddKodeProdukForm);
                setShowAddModal(false);
                HandleGetAllKodeProdukApi();
            } else {
                console.log("error on AddProject ", res.message);
            }
        } catch (error) {
            console.log("error on AddProject ", error);
        } finally {
            setAddLoading(false);
        }
    };

    const HandleEditKodeProdukApi = async () => {
        const errors = {};

        if (!editKodeProdukForm.project_name || editKodeProdukForm.project_name.trim() === "") {
            errors.project_name = "Nama Project wajib diisi";
        }

        if (!editKodeProdukForm.department) {
            errors.department = "Department wajib dipilih";
        }

        if (!editKodeProdukForm.pic) {
            errors.pic = "PIC wajib dipilih";
        }

        if (!editKodeProdukForm.pic_technical || editKodeProdukForm.pic_technical.trim() === "") {
            errors.pic_technical = "PIC Technical wajib diisi";
        }

        // If there are any errors, set them in the state
        if (Object.keys(errors).length > 0) {
            setErrorsEditForm(errors);
            return;
        }

        setAddLoading(true);

        try {
            const res = await ApiProjectPostgress().editMasterKodeProduk({
                ...editKodeProdukForm,
                diubah_oleh: user.no_karyawan + " - " + user.username,
            });

            if (res.status === 200) {
                HandleGetAllKodeProdukApi();
                setShowEditModal(false);
                setActiveEditTab('general');
            } else {
                console.log("error on editKodeProdukApi ", res.message);
            }
        } catch (error) {
            console.log("error on editKodeProdukApi ", error.message);
        } finally {
            setAddLoading(false);
        }
    };

    const handleEditStatusKodeProdukApi = async (id_produk, status) => {
        try {
            const res = await ApiProjectPostgress().editStatusMasterKodeProduk({
                id_produk,
                status,
                dihapus_oleh: user.no_karyawan + " - " + user.username,
            });

            if (res.status === 200) {
                console.log("Status update success:", res.message);
                HandleGetAllKodeProdukApi();
            } else {
                console.log("Error on update status: ", res.message);
            }
        } catch (error) {
            console.log("Error on update status: ", error.message);
        }
    };

    // Data untuk SelectPicker
    const departmentOptions = [
        { label: 'ALL DEPT', value: 'ALL DEPT' },
        { label: 'IT', value: 'IT' },
        { label: 'HR', value: 'HR' },
        { label: 'FINANCE', value: 'FINANCE' },
        { label: 'PURCHASING', value: 'PURCHASING' },
        { label: 'MARKETING', value: 'MARKETING' },
        { label: 'OPERATIONS', value: 'OPERATIONS' },
        { label: 'SALES', value: 'SALES' },
        { label: 'R&D', value: 'R&D' },
        { label: 'LEGAL', value: 'LEGAL' },
        { label: 'QUALITY', value: 'QUALITY' },
        { label: 'SUPPLY CHAIN', value: 'SUPPLY CHAIN' },
        { label: 'CPRO', value: 'CPRO' },
        { label: 'WHS', value: 'WHS' }
    ];

    const functionalOptions = [
        { label: 'YUNI', value: 'YUNI' },
        { label: 'DIDIT', value: 'DIDIT' },
        { label: 'RIAN', value: 'RIAN' },
        { label: 'AGUS', value: 'AGUS' }
    ];

    return (
        <div>
            <style>
                {`
                .expand-text-wrapper {
            /* Default: potong teks dalam satu baris */
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            padding: 8px; /* Menambah sedikit padding */
            margin: -8px; /* Menyeimbangkan padding dari cell bawaan */
        }

        .rs-table-cell:hover .expand-text-wrapper {
            /* Saat di-hover: biarkan teks turun ke bawah */
            white-space: normal;
            overflow: visible;
            
            /* Efek tambahan agar lebih menarik */
            background-color: #f7f7fa;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            position: relative;
            z-index: 10;
        }
        /* Selector final: menargetkan 'span' di dalam '.rs-progress-circle-info' */
        .progress-cell-small .rs-progress-circle-info > span {
            font-size: 8px !important;
            /* Opsional: membuat angka kecil lebih tebal agar mudah dibaca */
            font-weight: 600 !important; 
        }

        /* Selector untuk ikon sudah benar */
        .progress-cell-small .rs-icon {
            font-size: 15px !important;
        }
    `}
            </style>
            <div>
                <Head>
                    <title>Masterdata Projek</title>
                </Head>
            </div>
            <ContainerLayout title="User Module">
                <div className="m-4 pt-2">
                    <Stack alignItems="flex-start" spacing={10} className="mb-2">
                        <Stack.Item grow={1}>
                            <div>
                                <Breadcrumb>
                                    <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                                    <Breadcrumb.Item active>Halaman Masterdata Projek</Breadcrumb.Item>
                                </Breadcrumb>
                            </div>
                        </Stack.Item>
                        <Stack.Item>
                            <div>
                                <Tag color="green">Module: {moduleName}</Tag>
                            </div>
                        </Stack.Item>
                    </Stack>
                    <Panel
                        bordered
                        bodyFill
                        className="mb-3"
                        header={
                            <Stack justifyContent="space-between">
                                <h5>Halaman Masterdata Projek</h5>
                            </Stack>
                        }
                    ></Panel>
                    <div>
                        <Panel
                            bordered
                            bodyFill
                            header={
                                <Stack justifyContent="space-between">
                                    <div className="flex gap-2">
                                        <IconButton
                                            icon={<PlusRoundIcon />}
                                            appearance="primary"
                                            onClick={() => {
                                                setShowAddModal(true);
                                            }}
                                        >
                                            Tambah
                                        </IconButton>
                                    </div>
                                    <InputGroup inside>
                                        <InputGroup.Addon>
                                            <SearchIcon />
                                        </InputGroup.Addon>
                                        <Input placeholder="search" value={searchKeyword} onChange={handleSearch} />
                                        <InputGroup.Addon
                                            onClick={() => {
                                                setSearchKeyword("");
                                                setPage(1);
                                            }}
                                            style={{
                                                display: searchKeyword ? "block" : "none",
                                                color: "red",
                                                cursor: "pointer",
                                            }}
                                        >
                                            <CloseOutlineIcon />
                                        </InputGroup.Addon>
                                    </InputGroup>
                                </Stack>
                            }
                        >

                            <Table
                                bordered
                                cellBordered
                                autoHeight={true}
                                data={getPaginatedData(getFilteredData(), limit, page)}
                                wordWrap="break-word"
                                sortColumn={sortColumn}
                                sortType={sortType}
                                onSortColumn={handleSortColumn}

                            >
                                <Column width={70} align='center' fixed>
                                    <HeaderCell>No</HeaderCell>
                                    <Cell>
                                        {(_, index) => {
                                            return index + 1 + limit * (page - 1);
                                        }}
                                    </Cell>
                                </Column>
                                <Column width={70} sortable fullText resizable>
                                    <HeaderCell align="center">Kode Project</HeaderCell>
                                    <Cell dataKey="project_code" />
                                </Column>
                                <Column width={180} sortable resizable>
                                    <HeaderCell align="center">Nama Projek</HeaderCell>
                                    <Cell>
                                        {(rowData) => (
                                            <div className="expand-text-wrapper">
                                                {rowData.project_name}
                                            </div>
                                        )}
                                    </Cell>
                                </Column>

                                <Column width={120} sortable fullText resizable>
                                    <HeaderCell align="center">Department</HeaderCell>
                                    <Cell dataKey="department" />
                                </Column>

                                <Column width={120} sortable fullText resizable>
                                    <HeaderCell align="center">PIC</HeaderCell>
                                    <Cell dataKey="pic" />
                                </Column>

                                <Column width={150} sortable fullText resizable>
                                    <HeaderCell align="center">PIC Teknis</HeaderCell>
                                    <Cell dataKey="pic_technical" />
                                </Column>
                                <Column width={150} sortable resizable align="center" fullText>
                                    <HeaderCell>Estimasi Mulai</HeaderCell>
                                    <Cell>
                                        {(rowData) =>
                                            rowData.est_start_date
                                                ? new Date(rowData.est_start_date).toLocaleDateString("en-GB")
                                                : "-"
                                        }
                                    </Cell>
                                </Column>
                                <Column width={150} sortable resizable align="center" fullText>
                                    <HeaderCell>Estimasi Selesai</HeaderCell>
                                    <Cell>
                                        {(rowData) =>
                                            rowData.est_finish_date
                                                ? new Date(rowData.est_finish_date).toLocaleDateString("en-GB")
                                                : "-"
                                        }
                                    </Cell>
                                </Column>
                                <Column width={150} sortable resizable align="center" fullText>
                                    <HeaderCell>Aktual Mulai</HeaderCell>
                                    <Cell>
                                        {(rowData) =>
                                            rowData.act_start_date
                                                ? new Date(rowData.act_start_date).toLocaleDateString("en-GB")
                                                : "-"
                                        }
                                    </Cell>
                                </Column>
                                <Column width={150} sortable resizable align="center" fullText>
                                    <HeaderCell>Aktual Selesai</HeaderCell>
                                    <Cell>
                                        {(rowData) =>
                                            rowData.act_finish_date
                                                ? new Date(rowData.act_finish_date).toLocaleDateString("en-GB")
                                                : "-"
                                        }
                                    </Cell>
                                </Column>
                                <Column width={120} fixed="right" align="center">
                                    <HeaderCell>Progress</HeaderCell>
                                    {/* Tambahkan className di sini */}
                                    <Cell className="progress-cell-small">
                                        {(rowData) => (
                                            <Progress.Circle
                                                percent={rowData.progress}
                                                style={{ width: 25, height: 25 }}
                                                status={rowData.progress === 100 ? 'success' : null}
                                                strokeColor={rowData.progress < 100 ? getProgressColor(rowData.progress) : undefined}
                                            />
                                        )}
                                    </Cell>
                                </Column>

                                <Column width={200} sortable resizable>
                                    <HeaderCell align="center">Task of Development</HeaderCell>
                                    <Cell>
                                        {(rowData) => (
                                            <div className="expand-text-wrapper">
                                                {rowData.task_of_development || "-"}
                                            </div>
                                        )}
                                    </Cell>
                                </Column>

                                <Column width={200} sortable resizable>
                                    <HeaderCell align="center">Next To Do</HeaderCell>
                                    <Cell>
                                        {(rowData) => (
                                            <div className="expand-text-wrapper">
                                                {rowData.next_to_do || "-"}
                                            </div>
                                        )}
                                    </Cell>
                                </Column>


                                <Column width={120} fixed="right" align="center">
                                    <HeaderCell>Aksi</HeaderCell>
                                    <Cell style={{ padding: "8px" }}>
                                        {(rowData) => (
                                            <div>
                                                <Button
                                                    appearance="subtle"
                                                    disabled={rowData.status === 0}
                                                    onClick={() => {
                                                        setShowEditModal(true);
                                                        setActiveEditTab('general');
                                                        setEditKodeProdukForm({
                                                            id_produk: rowData.id_produk,
                                                            project_code: rowData.project_code,
                                                            project_name: rowData.project_name,
                                                            department: rowData.department,
                                                            pic: rowData.pic,
                                                            pic_technical: rowData.pic_technical,
                                                            est_start_date: rowData.est_start_date ? new Date(rowData.est_start_date) : null,
                                                            est_finish_date: rowData.est_finish_date ? new Date(rowData.est_finish_date) : null,
                                                            act_start_date: rowData.act_start_date ? new Date(rowData.act_start_date) : null,
                                                            act_finish_date: rowData.act_finish_date ? new Date(rowData.act_finish_date) : null,
                                                            progress: rowData.progress || 0,
                                                            task_of_development: rowData.task_of_development || "",
                                                            next_to_do: rowData.next_to_do || "",
                                                        });
                                                    }}
                                                >
                                                    <EditIcon />
                                                </Button>
                                                <Button appearance="subtle" onClick={() => handleEditStatusKodeProdukApi(rowData.id_produk, rowData.status)}>
                                                    {rowData.status === 1 ? <TrashIcon style={{ fontSize: "16px" }} /> : <ReloadIcon style={{ fontSize: "16px" }} />}
                                                </Button>
                                            </div>
                                        )}
                                    </Cell>
                                </Column>
                            </Table>

                            <div style={{ padding: 20 }}>
                                <Pagination
                                    prev
                                    next
                                    first
                                    last
                                    ellipsis
                                    boundaryLinks
                                    maxButtons={5}
                                    size="xs"
                                    layout={["total", "-", "limit", "|", "pager", "skip"]}
                                    limitOptions={[10, 30, 50]}
                                    total={totalRowCount}
                                    limit={limit}
                                    activePage={page}
                                    onChangePage={setPage}
                                    onChangeLimit={handleChangeLimit}
                                />
                            </div>
                        </Panel>

                        <Modal
                            backdrop="static"
                            open={showAddModal}
                            onClose={() => {
                                setShowAddModal(false);
                                setAddKodeProdukForm(emptyAddKodeProdukForm);
                                setErrorsAddForm({});
                            }}
                            size="md"
                            overflow={false}
                        >
                            <Modal.Header>
                                <Modal.Title>
                                    <div className="flex items-center gap-2">
                                        <PlusRoundIcon style={{ color: '#00a651' }} />
                                        <span>Tambah Project Baru</span>
                                    </div>
                                </Modal.Title>
                            </Modal.Header>
                            <Modal.Body style={{ padding: '24px' }}>
                                <Form fluid>
                                    {/* Row 1: Project Code & Name */}
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                        <Form.Group>
                                            <Form.ControlLabel className="font-semibold">
                                                Kode Project <span style={{ color: 'red' }}>*</span>
                                            </Form.ControlLabel>
                                            <Form.Control
                                                name="project_code"
                                                placeholder="Masukkan kode project"
                                                value={addKodeProdukForm.project_code}
                                                onChange={(value) => {
                                                    setAddKodeProdukForm(prev => ({ ...prev, project_code: value }));
                                                    setErrorsAddForm(prev => ({ ...prev, project_code: undefined }));
                                                }}
                                                style={{ borderRadius: '8px' }}
                                            />
                                            {errorsAddForm.project_code && (
                                                <div style={{ color: "red", fontSize: "12px", marginTop: "4px" }}>
                                                    {errorsAddForm.project_code}
                                                </div>
                                            )}
                                        </Form.Group>

                                        <Form.Group>
                                            <Form.ControlLabel className="font-semibold">
                                                Nama Project <span style={{ color: 'red' }}>*</span>
                                            </Form.ControlLabel>
                                            <Form.Control
                                                name="project_name"
                                                placeholder="Masukkan nama project"
                                                value={addKodeProdukForm.project_name}
                                                onChange={(value) => {
                                                    setAddKodeProdukForm(prev => ({ ...prev, project_name: value }));
                                                    setErrorsAddForm(prev => ({ ...prev, project_name: undefined }));
                                                }}
                                                style={{ borderRadius: '8px' }}
                                            />
                                            {errorsAddForm.project_name && (
                                                <div style={{ color: "red", fontSize: "12px", marginTop: "4px" }}>
                                                    {errorsAddForm.project_name}
                                                </div>
                                            )}
                                        </Form.Group>
                                    </div>

                                    {/* Row 2: Department & Functional */}
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                        <Form.Group>
                                            <Form.ControlLabel className="font-semibold">
                                                Department <span style={{ color: 'red' }}>*</span>
                                            </Form.ControlLabel>
                                            <SelectPicker
                                                data={departmentOptions}
                                                value={addKodeProdukForm.department}
                                                onChange={(value) => {
                                                    setAddKodeProdukForm(prev => ({ ...prev, department: value }));
                                                    setErrorsAddForm(prev => ({ ...prev, department: undefined }));
                                                }}
                                                placeholder="Pilih department"
                                                searchable={false}
                                                cleanable={false}
                                                style={{ width: '100%' }}
                                                menuStyle={{ zIndex: 1050 }}
                                            />
                                            {errorsAddForm.department && (
                                                <div style={{ color: "red", fontSize: "12px", marginTop: "4px" }}>
                                                    {errorsAddForm.department}
                                                </div>
                                            )}
                                        </Form.Group>

                                        <Form.Group>
                                            <Form.ControlLabel className="font-semibold">
                                                PIC <span style={{ color: 'red' }}>*</span>
                                            </Form.ControlLabel>
                                            <SelectPicker
                                                data={functionalOptions}
                                                value={addKodeProdukForm.pic}
                                                onChange={(value) => {
                                                    setAddKodeProdukForm(prev => ({ ...prev, pic: value }));
                                                    setErrorsAddForm(prev => ({ ...prev, pic: undefined }));
                                                }}
                                                placeholder="Pilih PIC"
                                                searchable={false}
                                                cleanable={false}
                                                style={{ width: '100%' }}
                                                menuStyle={{ zIndex: 1050 }}
                                            />
                                            {errorsAddForm.pic && (
                                                <div style={{ color: "red", fontSize: "12px", marginTop: "4px" }}>
                                                    {errorsAddForm.pic}
                                                </div>
                                            )}
                                        </Form.Group>
                                    </div>

                                    {/* Row 3: PIC & PIC Technical */}
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">


                                        <Form.Group>
                                            <Form.ControlLabel className="font-semibold">
                                                PIC Technical <span style={{ color: 'red' }}>*</span>
                                            </Form.ControlLabel>
                                            <Form.Control
                                                name="pic_technical"
                                                placeholder="Masukkan nama PIC Technical"
                                                value={addKodeProdukForm.pic_technical}
                                                onChange={(value) => {
                                                    setAddKodeProdukForm(prev => ({ ...prev, pic_technical: value }));
                                                    setErrorsAddForm(prev => ({ ...prev, pic_technical: undefined }));
                                                }}
                                                style={{ borderRadius: '8px' }}
                                            />
                                            {errorsAddForm.pic_technical && (
                                                <div style={{ color: "red", fontSize: "12px", marginTop: "4px" }}>
                                                    {errorsAddForm.pic_technical}
                                                </div>
                                            )}
                                        </Form.Group>
                                    </div>

                                    {/* Divider */}
                                    <div style={{ borderTop: '1px solid #e5e5e5', margin: '20px 0' }}></div>
                                    <h6 style={{ color: '#666', marginBottom: '16px', fontWeight: '600' }}>
                                        Jadwal Project
                                    </h6>

                                    {/* Row 4: Estimated Dates */}
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                        <Form.Group>
                                            <Form.ControlLabel className="font-semibold">
                                                Estimasi Tanggal Mulai <span style={{ color: 'red' }}>*</span>
                                            </Form.ControlLabel>
                                            <DatePicker
                                                value={addKodeProdukForm.est_start_date}
                                                onChange={(value) => {
                                                    setAddKodeProdukForm(prev => ({ ...prev, est_start_date: value }));
                                                    setErrorsAddForm(prev => ({ ...prev, est_start_date: undefined }));
                                                }}
                                                format="yyyy-MM-dd"
                                                placeholder="Pilih tanggal mulai"
                                                style={{ width: '100%', borderRadius: '8px' }}
                                            />
                                            {errorsAddForm.est_start_date && (
                                                <div style={{ color: "red", fontSize: "12px", marginTop: "4px" }}>
                                                    {errorsAddForm.est_start_date}
                                                </div>
                                            )}
                                        </Form.Group>

                                        <Form.Group>
                                            <Form.ControlLabel className="font-semibold">
                                                Estimasi Tanggal Selesai <span style={{ color: 'red' }}>*</span>
                                            </Form.ControlLabel>
                                            <DatePicker
                                                value={addKodeProdukForm.est_finish_date}
                                                onChange={(value) => {
                                                    setAddKodeProdukForm(prev => ({ ...prev, est_finish_date: value }));
                                                    setErrorsAddForm(prev => ({ ...prev, est_finish_date: undefined }));
                                                }}
                                                format="yyyy-MM-dd"
                                                placeholder="Pilih tanggal selesai"
                                                style={{ width: '100%', borderRadius: '8px' }}
                                            />
                                            {errorsAddForm.est_finish_date && (
                                                <div style={{ color: "red", fontSize: "12px", marginTop: "4px" }}>
                                                    {errorsAddForm.est_finish_date}
                                                </div>
                                            )}
                                        </Form.Group>
                                    </div>

                                    {/* Row 5: Actual Dates */}
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <Form.Group>
                                            <Form.ControlLabel className="font-semibold text-gray-600">
                                                Aktual Tanggal Mulai
                                            </Form.ControlLabel>
                                            <DatePicker
                                                value={addKodeProdukForm.act_start_date}
                                                onChange={(value) => {
                                                    setAddKodeProdukForm(prev => ({ ...prev, act_start_date: value }));
                                                }}
                                                format="yyyy-MM-dd"
                                                placeholder="Pilih tanggal (opsional)"
                                                style={{ width: '100%', borderRadius: '8px' }}
                                            />
                                        </Form.Group>

                                        <Form.Group>
                                            <Form.ControlLabel className="font-semibold text-gray-600">
                                                Aktual Tanggal Selesai
                                            </Form.ControlLabel>
                                            <DatePicker
                                                value={addKodeProdukForm.act_finish_date}
                                                onChange={(value) => {
                                                    setAddKodeProdukForm(prev => ({ ...prev, act_finish_date: value }));
                                                }}
                                                format="yyyy-MM-dd"
                                                placeholder="Pilih tanggal (opsional)"
                                                style={{ width: '100%', borderRadius: '8px' }}
                                            />
                                        </Form.Group>
                                    </div>
                                </Form>
                            </Modal.Body>
                            <Modal.Footer style={{ padding: '16px 24px', borderTop: '1px solid #e5e5e5' }}>
                                <div className="flex justify-end gap-3">
                                    <Button
                                        onClick={() => {
                                            setShowAddModal(false);
                                            setAddKodeProdukForm(emptyAddKodeProdukForm);
                                            setErrorsAddForm({});
                                        }}
                                        appearance="subtle"
                                        style={{ borderRadius: '8px', padding: '8px 20px' }}
                                    >
                                        Batal
                                    </Button>
                                    <Button
                                        onClick={HandleAddProject}
                                        appearance="primary"
                                        loading={addLoading}
                                        disabled={addLoading || !isFormValid()}
                                        style={{
                                            borderRadius: '8px',
                                            padding: '8px 20px',
                                            backgroundColor: !isFormValid() || addLoading ? '#9ca3af' : '#00a651',
                                            borderColor: !isFormValid() || addLoading ? '#9ca3af' : '#00a651',
                                            cursor: !isFormValid() || addLoading ? 'not-allowed' : 'pointer',
                                            opacity: !isFormValid() || addLoading ? 0.7 : 1,
                                            transition: 'all 0.2s ease-in-out'
                                        }}
                                    >
                                        {addLoading ? 'Menyimpan...' : 'Tambah Project'}
                                    </Button>
                                </div>
                            </Modal.Footer>
                        </Modal>
                        <Modal
                            backdrop="static"
                            open={showEditModal}
                            onClose={() => {
                                setShowEditModal(false);
                                setEditKodeProdukForm(emptyEditKodeProdukForm);
                                setErrorsEditForm({});
                                setActiveEditTab('general');
                            }}
                            size="lg"
                            overflow={false}
                        >
                            <Modal.Header>
                                <Modal.Title>
                                    <div className="flex items-center gap-2">
                                        <EditIcon style={{ color: '#00a651' }} />
                                        <span>Edit Project</span>
                                    </div>
                                </Modal.Title>
                            </Modal.Header>
                            <Modal.Body style={{ padding: '24px' }}>
                                <Nav appearance="tabs" activeKey={activeEditTab} onSelect={setActiveEditTab} style={{ marginBottom: '20px' }}>
                                    <Nav.Item eventKey="general">Informasi Umum</Nav.Item>
                                    <Nav.Item eventKey="progress">Progress & Task</Nav.Item>
                                </Nav>

                                {activeEditTab === 'general' && (
                                    <Form fluid>
                                        {/* Row 1: Project Code & Name */}
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                            <Form.Group>
                                                <Form.ControlLabel className="font-semibold">
                                                    Kode Project <span style={{ color: 'red' }}>*</span>
                                                </Form.ControlLabel>
                                                <Form.Control
                                                    name="project_code"
                                                    placeholder="Kode project"
                                                    value={editKodeProdukForm.project_code}
                                                    readOnly
                                                    style={{ borderRadius: '8px', backgroundColor: '#f5f5f5' }}
                                                />
                                            </Form.Group>

                                            <Form.Group>
                                                <Form.ControlLabel className="font-semibold">
                                                    Nama Project <span style={{ color: 'red' }}>*</span>
                                                </Form.ControlLabel>
                                                <Form.Control
                                                    name="project_name"
                                                    placeholder="Masukkan nama project"
                                                    value={editKodeProdukForm.project_name}
                                                    onChange={(value) => {
                                                        setEditKodeProdukForm(prev => ({ ...prev, project_name: value }));
                                                        setErrorsEditForm(prev => ({ ...prev, project_name: undefined }));
                                                    }}
                                                    style={{ borderRadius: '8px' }}
                                                />
                                                {errorsEditForm.project_name && (
                                                    <div style={{ color: "red", fontSize: "12px", marginTop: "4px" }}>
                                                        {errorsEditForm.project_name}
                                                    </div>
                                                )}
                                            </Form.Group>
                                        </div>

                                        {/* Row 2: Department & PIC */}
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                            <Form.Group>
                                                <Form.ControlLabel className="font-semibold">
                                                    Department <span style={{ color: 'red' }}>*</span>
                                                </Form.ControlLabel>
                                                <SelectPicker
                                                    data={departmentOptions}
                                                    value={editKodeProdukForm.department}
                                                    onChange={(value) => {
                                                        setEditKodeProdukForm(prev => ({ ...prev, department: value }));
                                                        setErrorsEditForm(prev => ({ ...prev, department: undefined }));
                                                    }}
                                                    placeholder="Pilih department"
                                                    searchable={false}
                                                    cleanable={false}
                                                    style={{ width: '100%' }}
                                                    menuStyle={{ zIndex: 1050 }}
                                                />
                                                {errorsEditForm.department && (
                                                    <div style={{ color: "red", fontSize: "12px", marginTop: "4px" }}>
                                                        {errorsEditForm.department}
                                                    </div>
                                                )}
                                            </Form.Group>

                                            <Form.Group>
                                                <Form.ControlLabel className="font-semibold">
                                                    PIC <span style={{ color: 'red' }}>*</span>
                                                </Form.ControlLabel>
                                                <SelectPicker
                                                    data={functionalOptions}
                                                    value={editKodeProdukForm.pic}
                                                    onChange={(value) => {
                                                        setEditKodeProdukForm(prev => ({ ...prev, pic: value }));
                                                        setErrorsEditForm(prev => ({ ...prev, pic: undefined }));
                                                    }}
                                                    placeholder="Pilih PIC"
                                                    searchable={false}
                                                    cleanable={false}
                                                    style={{ width: '100%' }}
                                                    menuStyle={{ zIndex: 1050 }}
                                                />
                                                {errorsEditForm.pic && (
                                                    <div style={{ color: "red", fontSize: "12px", marginTop: "4px" }}>
                                                        {errorsEditForm.pic}
                                                    </div>
                                                )}
                                            </Form.Group>
                                        </div>

                                        {/* Row 3: PIC Technical */}
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                            <Form.Group>
                                                <Form.ControlLabel className="font-semibold">
                                                    PIC Technical <span style={{ color: 'red' }}>*</span>
                                                </Form.ControlLabel>
                                                <Form.Control
                                                    name="pic_technical"
                                                    placeholder="Masukkan nama PIC Technical"
                                                    value={editKodeProdukForm.pic_technical}
                                                    onChange={(value) => {
                                                        setEditKodeProdukForm(prev => ({ ...prev, pic_technical: value }));
                                                        setErrorsEditForm(prev => ({ ...prev, pic_technical: undefined }));
                                                    }}
                                                    style={{ borderRadius: '8px' }}
                                                />
                                                {errorsEditForm.pic_technical && (
                                                    <div style={{ color: "red", fontSize: "12px", marginTop: "4px" }}>
                                                        {errorsEditForm.pic_technical}
                                                    </div>
                                                )}
                                            </Form.Group>
                                        </div>

                                        {/* Divider */}
                                        <div style={{ borderTop: '1px solid #e5e5e5', margin: '20px 0' }}></div>
                                        <h6 style={{ color: '#666', marginBottom: '16px', fontWeight: '600' }}>
                                            Jadwal Project
                                        </h6>

                                        {/* Row 4: Estimated Dates (Read Only) */}
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                            <Form.Group>
                                                <Form.ControlLabel className="font-semibold">
                                                    Estimasi Tanggal Mulai
                                                </Form.ControlLabel>
                                                <DatePicker
                                                    value={editKodeProdukForm.est_start_date}
                                                    format="yyyy-MM-dd"
                                                    placeholder="Estimasi tanggal mulai"
                                                    style={{ width: '100%', borderRadius: '8px', backgroundColor: '#f5f5f5' }}
                                                    readOnly
                                                />
                                            </Form.Group>

                                            <Form.Group>
                                                <Form.ControlLabel className="font-semibold">
                                                    Estimasi Tanggal Selesai
                                                </Form.ControlLabel>
                                                <DatePicker
                                                    value={editKodeProdukForm.est_finish_date}
                                                    format="yyyy-MM-dd"
                                                    placeholder="Estimasi tanggal selesai"
                                                    style={{ width: '100%', borderRadius: '8px', backgroundColor: '#f5f5f5' }}
                                                    readOnly
                                                />
                                            </Form.Group>
                                        </div>

                                        {/* Row 5: Actual Dates */}
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            <Form.Group>
                                                <Form.ControlLabel className="font-semibold text-gray-600">
                                                    Aktual Tanggal Mulai
                                                </Form.ControlLabel>
                                                <DatePicker
                                                    value={editKodeProdukForm.act_start_date}
                                                    onChange={(value) => {
                                                        setEditKodeProdukForm(prev => ({ ...prev, act_start_date: value }));
                                                    }}
                                                    format="yyyy-MM-dd"
                                                    placeholder="Pilih tanggal (opsional)"
                                                    style={{ width: '100%', borderRadius: '8px' }}
                                                />
                                            </Form.Group>

                                            <Form.Group>
                                                <Form.ControlLabel className="font-semibold text-gray-600">
                                                    Aktual Tanggal Selesai
                                                </Form.ControlLabel>
                                                <DatePicker
                                                    value={editKodeProdukForm.act_finish_date}
                                                    onChange={(value) => {
                                                        setEditKodeProdukForm(prev => ({ ...prev, act_finish_date: value }));
                                                    }}
                                                    format="yyyy-MM-dd"
                                                    placeholder="Pilih tanggal (opsional)"
                                                    style={{ width: '100%', borderRadius: '8px' }}
                                                />
                                            </Form.Group>
                                        </div>
                                    </Form>
                                )}

                                {activeEditTab === 'progress' && (
                                    <Form fluid>
                                        {/* Progress */}
                                        <Form.Group>
                                            <Form.ControlLabel className="font-semibold">
                                                Progress (%) <span style={{ color: 'red' }}>*</span>
                                            </Form.ControlLabel>
                                            <InputNumber
                                                value={editKodeProdukForm.progress}
                                                onChange={(value) => {
                                                    const numValue = Math.min(Math.max(parseFloat(value) || 0, 0), 100);
                                                    setEditKodeProdukForm(prev => ({ ...prev, progress: numValue }));
                                                }}
                                                min={0}
                                                max={100}
                                                step={1}
                                                placeholder="Masukkan progress (0-100)"
                                                style={{ width: '100%', borderRadius: '8px' }}
                                            />
                                            <div style={{ marginTop: '8px' }}>
                                                <Progress.Line
                                                    percent={editKodeProdukForm.progress}
                                                    status={editKodeProdukForm.progress === 100 ? 'success' : null}
                                                    strokeColor={editKodeProdukForm.progress < 100 ? getProgressColor(editKodeProdukForm.progress) : undefined}
                                                />
                                            </div>
                                        </Form.Group>

                                        {/* Task of Development */}
                                        <Form.Group>
                                            <Form.ControlLabel className="font-semibold">
                                                Task of Development
                                            </Form.ControlLabel>
                                            <Textarea
                                                rows={4}
                                                value={editKodeProdukForm.task_of_development}
                                                onChange={(value) => {
                                                    setEditKodeProdukForm(prev => ({ ...prev, task_of_development: value }));
                                                }}
                                                placeholder="Masukkan detail task development..."
                                                style={{ borderRadius: '8px' }}
                                            />
                                        </Form.Group>

                                        {/* Next To Do */}
                                        <Form.Group>
                                            <Form.ControlLabel className="font-semibold">
                                                Next To Do
                                            </Form.ControlLabel>
                                            <Textarea
                                                rows={4}
                                                value={editKodeProdukForm.next_to_do}
                                                onChange={(value) => {
                                                    setEditKodeProdukForm(prev => ({ ...prev, next_to_do: value }));
                                                }}
                                                placeholder="Masukkan rencana selanjutnya..."
                                                style={{ borderRadius: '8px' }}
                                            />
                                        </Form.Group>
                                    </Form>
                                )}
                            </Modal.Body>
                            <Modal.Footer style={{ padding: '16px 24px', borderTop: '1px solid #e5e5e5' }}>
                                <div className="flex justify-end gap-3">
                                    <Button
                                        onClick={() => {
                                            setShowEditModal(false);
                                            setEditKodeProdukForm(emptyEditKodeProdukForm);
                                            setErrorsEditForm({});
                                            setActiveEditTab('general');
                                        }}
                                        appearance="subtle"
                                        style={{ borderRadius: '8px', padding: '8px 20px' }}
                                    >
                                        Batal
                                    </Button>
                                    <Button
                                        onClick={HandleEditKodeProdukApi}
                                        appearance="primary"
                                        loading={addLoading}
                                        disabled={addLoading}
                                        style={{
                                            borderRadius: '8px',
                                            padding: '8px 20px',
                                            backgroundColor: addLoading ? '#9ca3af' : '#00a651',
                                            borderColor: addLoading ? '#9ca3af' : '#00a651',
                                        }}
                                    >
                                        {addLoading ? 'Menyimpan...' : 'Simpan Perubahan'}
                                    </Button>
                                </div>
                            </Modal.Footer>
                        </Modal>
                    </div>

                </div>

            </ContainerLayout>
        </div>
    );
}

export default withRoleAccess(MasterdataProject, rolePermissions['sisko/masterdata/kode_produk']);
