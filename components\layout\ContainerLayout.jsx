import { useRouter } from "next/router";
import React, { useState } from "react";
import {
  Container, Sidebar, Sidenav, Content, Nav, IconButton, Stack,
} from "rsuite";
import DashboardIcon from "@rsuite/icons/Dashboard";
import ListIcon from '@rsuite/icons/List';
import Header from "@/components/layout/Header";
import {
  MdKeyboardArrowLeft,
  MdOutlineKeyboardArrowRight
} from 'react-icons/md';
import { useUser } from "@/context/UserContext";
import MemberIcon from '@rsuite/icons/Member';
import PageIcon from '@rsuite/icons/Page';
import GearIcon from '@rsuite/icons/Gear';
import PeoplesIcon from '@rsuite/icons/Peoples';
import GridIcon from '@rsuite/icons/Grid';
import PeopleRuleIcon from '@rsuite/icons/PeopleRule';
import PeopleBranchIcon from '@rsuite/icons/PeopleBranch';
import BarLineChartIcon from '@rsuite/icons/BarLineChart';
import TreeIcon from '@rsuite/icons/Tree';


const headerStyles = {
  padding: 18,
  fontSize: 16,
  color: " #fff",
  whiteSpace: "nowrap",
  overflow: "hidden",
};

// Custom styles for active nav items
const customNavStyles = {
  activeNavItem: {
    backgroundColor: 'rgba(0, 128, 0, 0.1)',
    color: '#00a651',
    fontWeight: 'bold',
  },
  activeNavItemIcon: {
    color: '#00a651',
  }
};

// Define all possible nav links
// Old navigation links - commented out
/*
const ALL_NAV_LINKS = [
  { name: 'Dashboard', route: 'dashboard', icon: <DashboardIcon /> },
  { name: 'E Release List', route: 'user_module/pqr/reporting/list', icon: <ListIcon /> },
  { name: 'E Release Approval', route: 'user_module/pqr/supervisor/approval', icon: <ListIcon /> },
  { name: 'E Release Creation', route: 'user_module/pqr/operator/creation/list', icon: <ListIcon /> },
  { name: 'E Release Reporting', route: 'user_module/pqr/reporting/list/pqr', icon: <ListIcon /> },
  { name: 'Masterdata PPI', route: 'qs/masterdata/ppi', icon: <ListIcon /> },
  { name: 'Masterdata Step', route: 'qs/masterdata/step', icon: <ListIcon /> },
  { name: 'Masterdata Parameter', route: 'qs/masterdata/parameter', icon: <ListIcon /> },
  { name: 'Masterdata user', route: 'masterdata-user', icon: <ListIcon /> },
  { name: 'Masterdata roles', route: 'masterdata-roles', icon: <ListIcon /> },
  { name: 'testing', route: 'testing-inimah', icon: <ListIcon /> },
  // Tambahkan menu lain sesuai kebutuhan
];
*/

const ALL_NAV_LINKS = [
  { name: 'Dashboard', route: 'dashboard', icon: <DashboardIcon /> },
  { name: 'Project', route: 'project', icon: <TreeIcon /> },
  { name: 'Project Track', route: 'project_track', icon: <MemberIcon /> },
  {
    name: 'Monitoring',
    icon: <BarLineChartIcon />,
    children: [
      { name: 'Sistem Monitoring', route: 'monitoring/sistem-monitoring', icon: null },
      { name: 'IoT Monitoring', route: 'monitoring/iot-monitoring', icon: null },
      { name: 'Web DNS', route: 'monitoring/web-dns', icon: null },
      { name: 'Helpdesk Dashboard', route: 'monitoring/helpdesk-dashboard', icon: null },
    ]
  },
  {
    name: 'User Management',
    icon: <PeoplesIcon />,
    children: [
      { name: 'Masterdata User', route: 'masterdata-users', icon: null },
      { name: 'Masterdata Roles', route: 'masterdata-roles', icon: null },
    ]
  }


];

const ROLE_NAV_MAP = {
  1: ALL_NAV_LINKS,
  2: [
    ALL_NAV_LINKS[0],
    ALL_NAV_LINKS[1],
    ALL_NAV_LINKS[3],
  ],
  3: [
    ALL_NAV_LINKS[0],
    ALL_NAV_LINKS[1],
    ALL_NAV_LINKS[5],
  ],
  4: [
    ALL_NAV_LINKS[0],
    ALL_NAV_LINKS[1],
    ALL_NAV_LINKS[2],
  ],
  // Tambahkan role lain jika ada
};

function ContainerLayout(props) {
  const router = useRouter();
  const [expand, setExpand] = useState(true);
  const currentPath = router.pathname;
  const { user } = useUser();

  //menu sesuai role user
  let navMenu = [];
  if (user && user.role_id && ROLE_NAV_MAP[user.role_id]) {
    navMenu = ROLE_NAV_MAP[user.role_id];
  } else {
    navMenu = [ALL_NAV_LINKS[0]];
  }

  const navItemHandler = (route) => {
    if (route) {
      router.push(`/${route}`);
    }
  };

  const NavToggle = ({ expand, onChange }) => (
    <Stack className="nav-toggle" justifyContent={expand ? 'flex-end' : 'center'}>
      <IconButton
        onClick={onChange}
        appearance="subtle"
        size="lg"
        icon={expand ? <MdKeyboardArrowLeft /> : <MdOutlineKeyboardArrowRight />}
      />
    </Stack>
  );

  // Check if a route is active
  const isRouteActive = (route) => {
    if (!route) return false;

    // Normalize paths for comparison
    const normalizedCurrentPath = currentPath.endsWith('/')
      ? currentPath.slice(0, -1)
      : currentPath;

    const normalizedRoute = route.endsWith('/')
      ? route.slice(0, -1)
      : route;

    // Exact match
    if (normalizedCurrentPath === `/${normalizedRoute}`) {
      return true;
    }

    // Check if it's a child path but not a sibling path
    if (normalizedCurrentPath.startsWith(`/${normalizedRoute}/`)) {
      // For parent routes like 'sisko/operator/creation', don't highlight when in a direct child route
      // that has its own menu item
      const remainingPath = normalizedCurrentPath.substring(`/${normalizedRoute}/`.length);

      // Check if this remaining path matches any other direct menu items
      const isDirectChildMenuItem = navMenu.some(item => {
        if (item.children) {
          return item.children.some(child =>
            child.route && remainingPath === child.route.substring(normalizedRoute.length + 1)
          );
        }
        return false;
      });

      return !isDirectChildMenuItem;
    }

    return false;
  };

  return (
    <div className="sidebar-page h-screen">
      <Container>
        <Sidebar
          className="shadow-sm"
          style={{ display: "flex", flexDirection: "column" }}
          width={expand ? 260 : 56}
          collapsible
        >
          <Sidenav.Header>
            <div style={headerStyles}>
              <img src="/Logo_kalbe_detail.png" width={150} />
            </div>
          </Sidenav.Header>
          <NavToggle expand={expand} onChange={() => setExpand(!expand)} />
          <Sidenav
            expanded={expand}
            defaultOpenKeys={["masterdata"]}
            appearance="subtle"
          >
            <Sidenav.Body>
              <Nav>
                {navMenu.map((item, idx) => {
                  if (item.children) {
                    const isMenuActive = item.children.some(child => isRouteActive(child.route));

                    return (
                      <Nav.Menu
                        key={`menu-${idx}`}
                        eventKey={`menu-${idx}`}
                        title={item.name}
                        icon={item.icon}
                        style={isMenuActive ? { color: '#00a651', fontWeight: 'bold' } : {}}
                      >
                        {item.children.map((child, childIdx) => {
                          const isActive = isRouteActive(child.route);
                          return (
                            <Nav.Item
                              key={child.route || `child-${childIdx}`}
                              eventKey={child.route || `child-${childIdx}`}
                              onClick={() => navItemHandler(child.route)}
                              icon={child.icon}
                              style={isActive ? customNavStyles.activeNavItem : {}}
                            >
                              {child.name}
                            </Nav.Item>
                          );
                        })}
                      </Nav.Menu>
                    );
                  } else {
                    const isActive = isRouteActive(item.route);
                    return (
                      <Nav.Item
                        key={item.route || idx}
                        eventKey={item.route || idx}
                        onClick={() => navItemHandler(item.route)}
                        icon={item.icon}
                        style={isActive ? customNavStyles.activeNavItem : {}}
                      >
                        {item.name}
                      </Nav.Item>
                    );
                  }
                })}
              </Nav>
            </Sidenav.Body>
          </Sidenav>
        </Sidebar>
        <Container>
          <Header />
          <Content
            style={{ ...props?.contentStyle }}
            className="overflow-auto"
          >
            {props?.children}
          </Content>
        </Container>
      </Container>
    </div>
  );
}

export default ContainerLayout;
