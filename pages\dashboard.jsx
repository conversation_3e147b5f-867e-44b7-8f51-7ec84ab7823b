import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import ContainerLayout from '@/components/layout/ContainerLayout';
import { useUser } from '../context/UserContext';
import withRoleAccess from '@/components/auth/withRoleAccess';
import ProjectGanttChart from '@/components/charts/ProjectGanttChart';
import DepartmentPictorialChart from '@/components/charts/DepartmentPictorialChart';
import rolePermissions from '@/utils/rolePermissions';
import ApiProjectOld from './api/project_old/api_project_old';
import {
    Chart as ChartJS,
    CategoryScale,
    LinearScale,
    BarElement,
    LineElement,
    PointElement,
    ArcElement,
    Title,
    Tooltip,
    Legend,
    Filler,
    RadialLinearScale
} from 'chart.js';
import { Bar, Doughnut } from 'react-chartjs-2';


ChartJS.register(
    CategoryScale, LinearScale, BarElement, LineElement, PointElement,
    ArcElement, Title, Tooltip, Legend, Filler, RadialLinearScale
);

const ProgressBar = ({ progress }) => {
    const getProgressColor = (progress) => {
        if (progress < 40) return 'bg-red-500';
        if (progress < 70) return 'bg-yellow-500';
        return 'bg-green-500';
    };

    return (
        <div className="w-full bg-gray-200 rounded-full h-2.5">
            <div
                className={`h-2.5 rounded-full ${getProgressColor(progress)}`}
                style={{ width: `${progress}%` }}
            ></div>
        </div>
    );
};

function Dashboard() {
    const router = useRouter();
    const { user, loading } = useUser();
    const [currentTime, setCurrentTime] = useState(new Date());


    const [projectDataByPeriod, setProjectDataByPeriod] = useState({
        all: null,
        '2024': null,
        '2023': null,
    });
    const [selectedPeriod, setSelectedPeriod] = useState('all');
    const [analyticsData, setAnalyticsData] = useState(null);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState(null);


    useEffect(() => {
        const fetchAllData = async () => {
            if (!user) return;

            setIsLoading(true);
            setError(null);

            try {

                const [allResponse, response2024, response2023] = await Promise.all([
                    ApiProjectOld().getAllOldProject(),
                    ApiProjectOld().getAllOldProject({ period: '2024' }),
                    ApiProjectOld().getAllOldProject({ period: '2023' })
                ]);

                setProjectDataByPeriod({
                    all: allResponse.data,
                    '2024': response2024.data,
                    '2023': response2023.data,
                });

            } catch (err) {
                console.error('Error fetching dashboard data:', err);
                setError(err.message || 'Gagal memuat data dashboard.');
            } finally {
                setIsLoading(false);
            }
        };

        fetchAllData();
    }, [user]);


    useEffect(() => {
        const dataForPeriod = projectDataByPeriod[selectedPeriod];

        if (dataForPeriod) {
            const processedData = processProjectData(dataForPeriod);
            setAnalyticsData(processedData);
        } else {
            setAnalyticsData(null);
        }
    }, [projectDataByPeriod, selectedPeriod]);


    const processProjectData = (data) => {
        if (!data) {
            return null;
        }

        const { finished_projects, ongoing_projects, waiting_projects, total_finish, total_ongoing, total_waiting } = data;

        const kpis = {
            totalProjects: total_finish + total_ongoing + total_waiting,
            completedProjects: total_finish,
            ongoingProjects: total_ongoing,
            notStartedProjects: total_waiting,
        };

        const projectStatusChart = {
            labels: ['Selesai', 'Berjalan', 'Belum Dimulai'],
            datasets: [{
                data: [total_finish, total_ongoing, total_waiting],
                backgroundColor: ['rgba(34, 197, 94, 0.8)', 'rgba(59, 130, 246, 0.8)', 'rgba(239, 68, 68, 0.8)'],
                borderColor: '#ffffff',
                borderWidth: 2,
            }]
        };

        const allProjects = [...finished_projects, ...ongoing_projects, ...waiting_projects];
        const deptCounts = allProjects.reduce((acc, project) => {
            const dept = project.department || 'Lainnya';
            acc[dept] = (acc[dept] || 0) + 1;
            return acc;
        }, {});

        const projectsByDeptChart = {
            labels: Object.keys(deptCounts),
            datasets: [{
                label: 'Jumlah Proyek',
                data: Object.values(deptCounts),
                backgroundColor: 'rgba(59, 130, 246, 0.8)',
                borderColor: 'rgba(59, 130, 246, 1)',
                borderWidth: 1,
            }]
        };

        const ongoingProjectsList = ongoing_projects
            .sort((a, b) => b.progress - a.progress)
            .slice(0, 5);

        return { kpis, projectStatusChart, projectsByDeptChart, ongoingProjectsList };
    };

    const chartOptions = { responsive: true, maintainAspectRatio: false, plugins: { legend: { position: 'top', }, }, scales: { y: { beginAtZero: true, }, }, };
    const doughnutOptions = { responsive: true, maintainAspectRatio: false, plugins: { legend: { position: 'bottom', }, }, };
    const barChartOptions = { responsive: true, maintainAspectRatio: false, plugins: { legend: { display: false } }, scales: { y: { beginAtZero: true } }, };

    useEffect(() => {
        const timer = setInterval(() => { setCurrentTime(new Date()); }, 1000);
        return () => clearInterval(timer);
    }, []);

    if (loading || !user) {
        return (
            <div className="min-h-screen flex items-center justify-center bg-gray-50">
                <div className="flex flex-col items-center space-y-4">
                    <div className="animate-spin rounded-full h-12 w-12 border-4 border-blue-500 border-t-transparent"></div>
                    <p className="text-gray-600 font-medium">Loading your dashboard...</p>
                </div>
            </div>
        );
    }

    const getCurrentTime = () => {
        const now = new Date();
        const hour = now.getHours();
        if (hour < 12) return "Good Morning";
        if (hour < 17) return "Good Afternoon";
        return "Good Evening";
    };

    const formatTime = (date) => date.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', second: '2-digit', hour12: false });
    const formatDate = (date) => date.toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' });

    const handleActionClick = (path) => { if (path) { router.push(path); } };
    const roleSpecificActions = {
        1: [{ title: "Manajemen Role", icon: "➕", color: "bg-green-500", path: "/masterdata-roles" }, { title: "Manajemen Karyawan", icon: "👥", color: "bg-blue-500", path: "masterdata-users" },],
        2: [{ title: "Manajemen PPR", icon: "📝", color: "bg-purple-500", path: "/sisko/masterdata/ppr" }, { title: "Tambah Indikator", icon: "➕", color: "bg-orange-500", path: "/sisko/masterdata/indikator" }],
        3: [{ title: "Persetujuan PPR", icon: "✅", color: "bg-teal-500", path: "/sisko/approval/ppr" }, { title: "Riwayat Persetujuan", icon: "📜", color: "bg-indigo-500", path: "/sisko/approval/reporting" }],
        4: [{ title: "Buat Transaksi", icon: "➕", color: "bg-red-500", path: "/sisko/operator/creation/addTransactionHeader" }, { title: "Persetujuan Transaksi", icon: "📋", color: "bg-yellow-500", path: "/sisko/operator/reporting" }],
        default: [{ title: "Lihat Dashboard", icon: "📊", color: "bg-blue-500", path: "/dashboard" }, { title: "Hubungi Bantuan", icon: "📞", color: "bg-gray-500", path: "/support" }]
    };
    const quickActions = roleSpecificActions[user.role_id] || roleSpecificActions.default;


    return (
        <ContainerLayout>
            <div className="space-y-6 mx-4 md:mx-6 lg:mx-8">
                <div className="bg-gradient-to-r from-green-600 to-emerald-600 rounded-2xl p-8 text-white shadow-xl">
                    <div className="flex flex-col md:flex-row items-start md:items-center justify-between">
                        <div>
                            <h1 className="text-3xl font-bold mb-2">
                                {getCurrentTime()}, {user.username}! 👋
                            </h1>
                            <p className="text-green-100 text-lg mb-4">
                                Welcome back to your dashboard. Here's what's happening today.
                            </p>
                        </div>

                        <div className="flex flex-row md:flex-col items-center mt-4 md:mt-0">
                            <div className="w-20 h-20 backdrop-blur-sm rounded-full flex items-center justify-center shadow-lg mb-0 md:mb-3">
                                <span className="text-3xl">🧪</span>
                            </div>
                            <div className="ml-4 md:ml-0 text-center">
                                <p className="text-white text-xl font-semibold">{formatTime(currentTime)}</p>
                                <p className="text-green-100 text-sm">{formatDate(currentTime)}</p>
                            </div>
                        </div>
                    </div>

                </div>
                <div className="space-y-6">
                    <div className="flex flex-col md:flex-row justify-between md:items-center">
                        <div className="flex items-center space-x-2 mt-4 md:mt-0 p-1 bg-gray-200 rounded-lg">
                            {['all', '2024', '2023'].map((period) => {
                                const isActive = selectedPeriod === period;
                                return (
                                    <button
                                        key={period}
                                        onClick={() => setSelectedPeriod(period)}
                                        className={`px-4 py-1.5 text-sm font-semibold rounded-md transition-colors duration-200 ${isActive ? 'bg-white text-green-600 shadow' : 'bg-transparent text-gray-600 hover:bg-gray-300'}`}
                                    >
                                        {period === 'all' ? 'Semua' : period}
                                    </button>
                                );
                            })}
                        </div>
                    </div>

                    {/* Indikator loading utama hanya untuk pengambilan data awal */}
                    {isLoading && (
                        <div className="text-center py-10">
                            <div className="animate-spin rounded-full h-8 w-8 border-4 border-blue-500 border-t-transparent mx-auto mb-4"></div>
                            <p>Memuat data analitik...</p>
                        </div>
                    )}
                    {error && <div className="text-center py-10 text-red-600 bg-red-100 p-4 rounded-lg">{error}</div>}

                    {/* Tampilkan data setelah loading selesai dan data analitik tersedia */}
                    {analyticsData && !isLoading && (
                        <>
                            {/* Kartu KPI Utama */}
                            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                                <div className="bg-white p-6 rounded-xl shadow-md border border-gray-100">
                                    <h3 className="text-gray-500 font-medium">Total Proyek</h3>
                                    <p className="text-3xl font-bold text-blue-600">{analyticsData.kpis.totalProjects}</p>
                                </div>
                                <div className="bg-white p-6 rounded-xl shadow-md border border-gray-100">
                                    <h3 className="text-gray-500 font-medium">Proyek Selesai</h3>
                                    <p className="text-3xl font-bold text-green-600">{analyticsData.kpis.completedProjects}</p>
                                </div>
                                <div className="bg-white p-6 rounded-xl shadow-md border border-gray-100">
                                    <h3 className="text-gray-500 font-medium">Proyek Berjalan</h3>
                                    <p className="text-3xl font-bold text-yellow-600">{analyticsData.kpis.ongoingProjects}</p>
                                </div>
                                <div className="bg-white p-6 rounded-xl shadow-md border border-gray-100">
                                    <h3 className="text-gray-500 font-medium">Belum Dimulai</h3>
                                    <p className="text-3xl font-bold text-red-600">{analyticsData.kpis.notStartedProjects}</p>
                                </div>
                            </div>
                            <div className="grid grid-cols-1 lg:grid-cols-5 gap-6">
                                {/* Grafik Status Proyek */}
                                <div className="lg:col-span-2 bg-white rounded-xl shadow-lg border border-gray-100 p-6">
                                    <h3 className="text-lg font-semibold text-gray-800 mb-4">Distribusi Status Proyek</h3>
                                    <div style={{ height: '300px' }}>
                                        <Doughnut data={analyticsData.projectStatusChart} options={doughnutOptions} />
                                    </div>
                                </div>
                                {/* Grafik Proyek per Departemen */}
                                {/* <div className="lg:col-span-3 bg-white rounded-xl shadow-lg border border-gray-100 p-6">
                                    <h3 className="text-lg font-semibold text-gray-800 mb-4">Jumlah Proyek per Departemen</h3>
                                    <div style={{ height: '300px' }}>
                                        <Bar data={analyticsData.projectsByDeptChart} options={barChartOptions} />
                                    </div>
                                </div> */}
                                {/* Grafik Proyek per Departemen */}
                                <div className="lg:col-span-3 bg-white rounded-xl shadow-lg border border-gray-100 p-6">
                                    <h3 className="text-lg font-semibold text-gray-800 mb-4">Jumlah Proyek per Departemen</h3>
                                    <DepartmentPictorialChart chartData={analyticsData.projectsByDeptChart} />
                                </div>

                                <div className="lg:col-span-5 bg-white rounded-xl shadow-lg border border-gray-100 p-6">
                                    <h3 className="text-lg font-semibold text-gray-800 mb-4">Proyek Berjalan Teratas</h3>

                                    {/* Menggunakan tabel untuk tampilan yang lebih ringkas */}
                                    <div className="overflow-x-auto">
                                        <table className="w-full text-sm text-left">
                                            <thead className="text-xs text-gray-700 uppercase bg-gray-50">
                                                <tr>
                                                    <th className="px-4 py-3">Nama Projek</th>
                                                    <th className="px-4 py-3">PIC</th>
                                                    <th className="px-4 py-3 w-1/3">Progress</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {analyticsData.ongoingProjectsList.length > 0 ? analyticsData.ongoingProjectsList.map(project => (
                                                    <tr key={project.project_code} className="bg-white border-b hover:bg-gray-50 align-middle">
                                                        <td className="px-4 py-3 font-medium text-gray-800">
                                                            {project.project_name}
                                                        </td>
                                                        <td className="px-4 py-3 text-gray-600">
                                                            {project.pic}
                                                        </td>
                                                        <td className="px-4 py-3">
                                                            <div className="flex items-center gap-3">
                                                                <ProgressBar progress={project.progress} />
                                                                <span className="font-semibold">{project.progress}%</span>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                )) : (
                                                    <tr>
                                                        <td colSpan="3" className="text-center py-5 text-gray-500">
                                                            Tidak ada proyek yang sedang berjalan.
                                                        </td>
                                                    </tr>
                                                )}
                                            </tbody>
                                        </table>
                                    </div>

                                </div>
                            </div>
                            {analyticsData && !isLoading && (
                                <ProjectGanttChart />
                            )}
                        </>
                    )}
                </div>
            </div>
        </ContainerLayout>
    );
}

export default withRoleAccess(Dashboard, rolePermissions['dashboard']);