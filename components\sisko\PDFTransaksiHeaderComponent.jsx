import { useEffect, useState } from "react";
import Head from "next/head";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "rsuite";
import { faFileDownload } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { useUser } from "@/context/UserContext";
import ApiSiskoTransactionHeader from "@/pages/api/sisko/transaction_h/api_sisko_transaction_h";
import ApiSiskoTransactionDetail from "@/pages/api/sisko/transaction_d/api_sisko_transaction_d";
import pdfMake from "pdfmake/build/pdfmake";
import pdfFonts from "pdfmake/build/vfs_fonts";

pdfMake.vfs = pdfFonts.vfs;

export default function PDFTransaksiHeaderComponent({ idTransHeader, sessionAuth }) {
  const { user } = useUser();
  const [transactionDetailDataState, setTransactionDetailDataState] = useState([]);
  const [formData, setFormData] = useState({
    id_transaksi_header: null,
    id_ppr: null,
    nama_ppr: null,
    kode_batch: null,
    catatan: null,
    wetmill: null,
    status_transaksi: null,
    status_persetujuan: null,
    disetujui_oleh: null,
    disetujui_tanggal: null,
    catatan_revisi: null,
    tanggal_dibuat: "",
    dibuat_oleh: "",
    tanggal_diubah: "",
    diubah_oleh: "",
    tanggal_dihapus: "",
    dihapus_oleh: "",
    status: null
  });

  const HandleGetAllTransactionDetailApi = async (id_transaksi_header) => {
    try {
      const res = await ApiSiskoTransactionDetail().getSiskoTransactionDetailByID({ id_transaksi_header: parseInt(id_transaksi_header) });
      console.log("res details", res);
      if (res.status === 200) {
        setTransactionDetailDataState([...res.data]);
        console.log("Updated Detail Data State:", res.data);
      } else {
        console.log("error on Get All Detail Api ", res.message);
      }
    } catch (error) {
      console.log("error on catch Get All Detail Api", error);
    }
  };

  const HandleGetTransactionHeader = async (id_transaksi_header) => {
    try {
      const api = ApiSiskoTransactionHeader();
      const response = await api.getPPRSiskoTransactionHeaderById({ id_transaksi_header: parseInt(id_transaksi_header) });

      if (response.status === 200) {
        const data = response.data;
        setFormData({
          id_transaksi_header: data.id_transaksi_header,
          id_ppr: data.id_ppr,
          nama_ppr: data.nama_ppr,
          kode_batch: data.kode_batch,
          catatan: data.catatan,
          wetmill: data.wetmill,
          status_transaksi: data.status_transaksi,
          status_persetujuan: data.status_persetujuan,
          disetujui_oleh: data.disetujui_oleh,
          disetujui_tanggal: data.disetujui_tanggal ? new Date(data.disetujui_tanggal).toLocaleDateString("en-GB") : "-",
          catatan_revisi: data.catatan_revisi,
          tanggal_dibuat: new Date(data.tanggal_dibuat).toLocaleDateString("en-GB"),
          dibuat_oleh: data.dibuat_oleh,
          tanggal_diubah: data.tanggal_diubah ? new Date(data.tanggal_diubah).toLocaleDateString("en-GB") : "-",
          diubah_oleh: data.diubah_oleh || "-",
          tanggal_dihapus: data.tanggal_dihapus ? new Date(data.tanggal_dihapus).toLocaleDateString("en-GB") : "-",
          dihapus_oleh: data.dihapus_oleh || "-",
        });
      } else {
        console.error("Failed to fetch header data");
      }
    } catch (error) {
      console.error("Error fetching header data:", error);
    }
  };

  useEffect(() => {
    if (idTransHeader) {
      HandleGetAllTransactionDetailApi(idTransHeader);
      HandleGetTransactionHeader(idTransHeader);
    }
  }, [idTransHeader]);

  const generatePdf = (action) => {
    if (!transactionDetailDataState || !Array.isArray(transactionDetailDataState)) {
      console.error("Data detail belum tersedia atau salah format:", transactionDetailDataState);
      return;
    }

    const formattedDate = (dateStr) => {
      if (!dateStr) return "-";
      const [day, month, year] = dateStr.split("/");

      const shortYear = year.slice(-2);
      return `${day}-${month}-${shortYear}`;
    };

    // Informasi header
    const createdBy = `${formData.dibuat_oleh} pada ${formattedDate(formData.tanggal_dibuat)}`;
    const approvedBy = formData.disetujui_oleh && formData.disetujui_tanggal
      ? `${formData.disetujui_oleh} pada ${formattedDate(formData.disetujui_tanggal)}`
      : "-";

    const headerSection = [
      { text: `Laporan Transaksi Header`, style: "header" },
      { text: `ID Transaksi      : ${formData.id_transaksi_header || '-'}`, style: "detail" },
      { text: `Nama PPR          : ${formData.nama_ppr || '-'}`, style: "detail" },
      { text: `Kode Batch        : ${formData.kode_batch || '-'}`, style: "detail" },
      { text: `Catatan           : ${formData.catatan || '-'}`, style: "detail" },
      { text: `Dibuat Oleh       : ${createdBy}`, style: "detail" },
      { text: `Disetujui Oleh    : ${approvedBy}`, style: "detail" },
      { text: "\n" }
    ];

    let contentArray = [];
    contentArray.push(...headerSection);


    contentArray.push({ text: "Rincian Transaksi Header", style: "sectionHeader" });
    contentArray.push({ text: "\n" });

    // Kelompokkan detail transaksi berdasarkan langkah
    if (transactionDetailDataState.length > 0) {
      const detailsByStep = {};

      transactionDetailDataState.forEach(detail => {
        const stepKey = `${detail.id_langkah}-${detail.nama_langkah}`;
        if (!detailsByStep[stepKey]) {
          detailsByStep[stepKey] = {
            id_langkah: detail.id_langkah,
            nama_langkah: detail.nama_langkah,
            details: []
          };
        }
        detailsByStep[stepKey].details.push(detail);
      });

      // Iterasi melalui setiap langkah dan buat tabel untuk setiap langkah
      Object.values(detailsByStep).forEach(step => {
        contentArray.push({ text: `Tahapan: ${step.nama_langkah}`, style: "subheader" });

        // Header tabel
        let tableBody = [
          [
            { text: "No", style: "tableHeader" },
            { text: "Nama Indikator", style: "tableHeader" },
            { text: "Wetmill", style: "tableHeader" },
            { text: "Syarat Standar", style: "tableHeader" },
            { text: "Nilai Aktual", style: "tableHeader" },
            { text: "Keterangan", style: "tableHeader" }
          ]
        ];

        // Isi tabel
        step.details.forEach((detail, index) => {
          let nilaiDisplay = "";

          // Menentukan tampilan nilai berdasarkan tipe binding
          if (detail.binding_type === 1) {
            nilaiDisplay = `${detail.min_value} - ${detail.max_value}`;
          } else if (detail.binding_type === 2) {
            nilaiDisplay = detail.absolute_value;
          } else if (detail.binding_type === 3) {
            nilaiDisplay = detail.description_value;
          } else {
            nilaiDisplay = detail.set_point_value || "-";
          }

          const wetmillDisplay = detail.wetmill === "Y" ? "Ya" :
            detail.wetmill === "N" ? "Tidak" : "-";

          tableBody.push([
            index + 1,
            `${detail.nama_indikator}`,
            wetmillDisplay,
            nilaiDisplay,
            detail.nilai_aktual || "-",
            detail.result || "-"
          ]);
        });

        contentArray.push({
          style: "tableExample",
          table: {
            headerRows: 1,
            widths: ["auto", "*", "auto", "auto", "auto", "auto"],
            body: tableBody
          }
        });

        contentArray.push({ text: "\n" });
      });

      // informasi status keseluruhan transaksi
      const hasTMS = transactionDetailDataState.some(detail => detail.result === "TMS");
      const statusMessage = hasTMS
        ? "Transaksi ini belum memenuhi standar yang ditetapkan. Mohon tinjau kembali agar sesuai dengan ketentuan."
        : "Transaksi ini telah memenuhi semua standar yang berlaku";

      contentArray.push({ text: "Hasil Laporan", style: "sectionHeader" });
      contentArray.push({ text: statusMessage, style: "statusInfo" });


    } else {
      contentArray.push({ text: "Tidak ada data detail transaksi", style: "subheader" });
    }

    // Format tanggal dan waktu saat ini
    const now = new Date();
    const formattedCurrentDate = now.toLocaleDateString("en-GB").split("/").join("-");
    const formattedCurrentTime = now.toLocaleTimeString("en-GB");
    const effectiveSessionAuth = sessionAuth || user;
    const footerText = `Dokumen ini disediakan oleh : ${effectiveSessionAuth?.username || '-'} pada ${formattedCurrentDate} ${formattedCurrentTime}`;


    var dd = {
      content: contentArray,
      styles: {
        header: {
          fontSize: 18,
          bold: true,
          margin: [0, 0, 0, 10]
        },
        subheader: {
          fontSize: 14,
          bold: true,
          margin: [0, 10, 0, 5]
        },
        detail: {
          fontSize: 12,
          margin: [0, 2, 0, 2]
        },
        tableExample: {
          margin: [0, 5, 0, 15]
        },
        tableHeader: {
          bold: true,
          fontSize: 12,
          color: "black"
        },
        sectionHeader: {
          fontSize: 16,
          bold: true,
          margin: [0, 10, 0, 5]
        },
        statusInfo: {
          fontSize: 14,
          bold: true,
          margin: [0, 5, 0, 10],
          color: "blue"
        }
      },
      footer: (currentPage, pageCount) => {
        return {
          columns: [
            { text: footerText, alignment: "left", margin: [30, 0, 0, 0] },
            { text: `Halaman ${currentPage} dari ${pageCount}`, alignment: "center", margin: [0, 0, 0, 0] },
            {
              text: [
                { text: "Keterangan:\n", bold: true },
                { text: "MS = Memenuhi Standar\n" },
                { text: "TMS = Tidak Memenuhi Standar" }
              ],
              alignment: "right",
              margin: [0, 5, 30, 0],
              fontSize: 9,
              italics: true
            }

          ]
        };
      },
      pageMargins: [40, 60, 40, 60]
    };

    // Buat dan tampilkan PDF
    if (action === "Download") {
      pdfMake.createPdf(dd).download(`Transaksi Header - ${formData.nama_ppr || `ID ${idTransHeader}`}.pdf`);
    } else {
      pdfMake.createPdf(dd).open();
    }
  };

  return (
    <div
      style={{
        width: "100%",
        padding: "1em",
        backgroundColor: "#2c2c30",
        position: "sticky",
        top: 0,
      }}
    >
      <Head>
        <title>Laporan Transaksi Header</title>
      </Head>
      <Stack justifyContent="space-between">
        <p style={{ color: "white", fontSize: "1em" }}>
          Cetak Laporan Transaksi Header - {formData.nama_ppr || `ID Transaksi: ${idTransHeader}`}
        </p>
        <Stack>
          <Button onClick={() => generatePdf("preview")} style={{ marginRight: "5px" }}>
            Preview
          </Button>
          <Button onClick={() => generatePdf("Download")}>
            <FontAwesomeIcon icon={faFileDownload} style={{ fontSize: 15 }} /> Download
          </Button>
        </Stack>
      </Stack>
    </div>
  );
}