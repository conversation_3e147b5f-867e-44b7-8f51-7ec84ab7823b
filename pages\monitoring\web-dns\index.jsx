import { useEffect, useState } from "react";
import Head from "next/head";
import ContainerLayout from "@/components/layout/ContainerLayout";
import { useRouter } from "next/router";
import { useUser } from "@/context/UserContext";
import withRoleAccess from "@/components/auth/withRoleAccess";
import rolePermissions from "@/utils/rolePermissions";

const SHAREPOINT_EMBED_URL =
    "https://kalbeconsumerhealth-my.sharepoint.com/:x:/g/personal/yustinus_wiratama_kalbeconsumerhealth_co_id/EQz9-eJU4CxGrMXGnTmZuNEBjM5oO3xstqLMTeKlPMtZjg?e=ymyIfP" +
    "&action=embedview&ActiveCell='SFL%20Website%20Domain%20List'!A1&wdHideGridlines=True&wdHideHeaders=True&wdInConfigurator=True";

function WebDNS() {
    const router = useRouter();
    const { user, isAuthenticated, loading: userLoading } = useUser();
    const [userName, setUserName] = useState("");

    useEffect(() => {
        if (userLoading) return;

        if (!isAuthenticated()) {
            router.push("/login");
            return;
        }
        setUserName(user.username || user.fullname || "User");
    }, [user, userLoading, isAuthenticated, router]);

    return (
        <div>
            <Head>
                <title>SFL Web & DNS - Monitoring</title>
                <link rel="icon" href="/img/logo-kalbe-white.png" />
            </Head>

            <ContainerLayout title="SFL Web & DNS">
                {/* Topbar Sederhana (mirip versi PHP) */}


                {/* Embed SharePoint Excel */}
                <div className="relative" style={{ height: "calc(100vh - 180px)" }}>
                    <iframe
                        src={SHAREPOINT_EMBED_URL}
                        title="SFL Website & DNS List"
                        width="100%"
                        height="100%"
                        style={{ border: "none", overflow: "hidden" }}
                        allowTransparency="true"
                    >
                        Your browser does not support iframes.
                    </iframe>
                </div>
            </ContainerLayout>
        </div>
    );
}
export default withRoleAccess(WebDNS, rolePermissions["sisko/masterdata/kode_produk"]);