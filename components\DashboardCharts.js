import React from 'react';
import {
    Chart as ChartJS,
    CategoryScale,
    LinearScale,
    BarElement,
    LineElement,
    PointElement,
    ArcElement,
    Title,
    Tooltip,
    Legend,
    Filler,
} from 'chart.js';
import { Bar, Line, Doughnut, Radar } from 'react-chartjs-2';

// Register Chart.js components
ChartJS.register(
    CategoryScale,
    LinearScale,
    BarElement,
    LineElement,
    PointElement,
    ArcElement,
    Title,
    Tooltip,
    Legend,
    Filler
);

const DashboardCharts = () => {
    // Chart options
    const chartOptions = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'top',
            },
        },
        scales: {
            y: {
                beginAtZero: true,
            },
        },
    };

    const doughnutOptions = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom',
            },
        },
    };

    const horizontalBarOptions = {
        responsive: true,
        maintainAspectRatio: false,
        indexAxis: 'y',
        plugins: {
            legend: {
                display: false,
            },
        },
        scales: {
            x: {
                beginAtZero: true,
            },
        },
    };

    const radarOptions = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'top',
            },
        },
        scales: {
            r: {
                beginAtZero: true,
                max: 100,
            },
        },
    };

    // Dummy data for charts
    const chartData = {
        pprApproved: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
            datasets: [{
                label: 'PPR Disetujui',
                data: [12, 19, 15, 25, 22, 30],
                backgroundColor: 'rgba(34, 197, 94, 0.8)',
                borderColor: 'rgba(34, 197, 94, 1)',
                borderWidth: 2,
            }]
        },
        transactionHeader: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
            datasets: [{
                label: 'Transaksi Header Disetujui',
                data: [65, 78, 90, 81, 95, 102],
                borderColor: 'rgba(59, 130, 246, 1)',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                fill: true,
                tension: 0.4,
            }]
        },
        productDistribution: {
            labels: ['Tersedia', 'Dalam Proses', 'Habis', 'Pending'],
            datasets: [{
                data: [45, 25, 15, 15],
                backgroundColor: [
                    'rgba(34, 197, 94, 0.8)',
                    'rgba(251, 191, 36, 0.8)', 
                    'rgba(239, 68, 68, 0.8)',
                    'rgba(156, 163, 175, 0.8)'
                ],
                borderWidth: 2,
            }]
        },
        qualityTrend: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
            datasets: [{
                label: 'Skor Kualitas (%)',
                data: [85, 88, 92, 89, 94, 96],
                borderColor: 'rgba(168, 85, 247, 1)',
                backgroundColor: 'rgba(168, 85, 247, 0.2)',
                fill: true,
                tension: 0.4,
            }]
        },
        topProducts: {
            labels: ['Paracetamol 500mg', 'Amoxicillin 250mg', 'Vitamin C 1000mg', 'Ibuprofen 400mg', 'Aspirin 100mg'],
            datasets: [{
                label: 'Jumlah Terjual',
                data: [320, 280, 250, 220, 180],
                backgroundColor: [
                    'rgba(34, 197, 94, 0.8)',
                    'rgba(59, 130, 246, 0.8)',
                    'rgba(251, 191, 36, 0.8)',
                    'rgba(168, 85, 247, 0.8)',
                    'rgba(236, 72, 153, 0.8)'
                ],
                borderWidth: 2,
            }]
        },
        systemPerformance: {
            labels: ['CPU Usage', 'Memory Usage', 'Disk Usage', 'Network Usage'],
            datasets: [{
                label: 'Usage (%)',
                data: [45, 62, 38, 25],
                backgroundColor: [
                    'rgba(34, 197, 94, 0.8)',
                    'rgba(251, 191, 36, 0.8)',
                    'rgba(59, 130, 246, 0.8)',
                    'rgba(168, 85, 247, 0.8)'
                ],
                borderWidth: 2,
            }]
        },
        monthlyComparison: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
            datasets: [
                {
                    label: 'PPR Disetujui',
                    data: [12, 19, 15, 25, 22, 30],
                    borderColor: 'rgba(34, 197, 94, 1)',
                    backgroundColor: 'rgba(34, 197, 94, 0.2)',
                    fill: false,
                },
                {
                    label: 'PPR Ditolak',
                    data: [3, 5, 2, 8, 4, 6],
                    borderColor: 'rgba(239, 68, 68, 1)',
                    backgroundColor: 'rgba(239, 68, 68, 0.2)',
                    fill: false,
                },
                {
                    label: 'PPR Pending',
                    data: [8, 12, 10, 15, 18, 12],
                    borderColor: 'rgba(251, 191, 36, 1)',
                    backgroundColor: 'rgba(251, 191, 36, 0.2)',
                    fill: false,
                }
            ]
        },
        departmentPerformance: {
            labels: ['Quality Control', 'Production', 'Research & Development', 'Regulatory Affairs', 'Supply Chain', 'Marketing'],
            datasets: [{
                label: 'Performance Score',
                data: [85, 92, 78, 88, 90, 82],
                backgroundColor: 'rgba(34, 197, 94, 0.2)',
                borderColor: 'rgba(34, 197, 94, 1)',
                borderWidth: 2,
            }]
        }
    };

    return { chartData, chartOptions, doughnutOptions, horizontalBarOptions, radarOptions };
};

export default DashboardCharts;
