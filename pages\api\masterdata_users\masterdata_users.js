import createApiFunction from "@/lib/apiClient";

export default function ApiMasterdataUsers() {
    return {
        getAllUsers: createApiFunction("get", "users/list"),
        getUserById: createApiFunction("post", "users/id"),
        createUser: createApiFunction("post", "users/create"),
        updateUser: createApiFunction("put", "users/edit"),
        updateUserStatus: createApiFunction("put", "users/edit-status"),
    };
}

