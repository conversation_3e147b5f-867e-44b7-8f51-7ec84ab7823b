import { useEffect, useState } from "react";
import Head from "next/head";
import ContainerLayout from "@/components/layout/ContainerLayout";
import { useRouter } from "next/router";
import { useUser } from "@/context/UserContext";
import withRoleAccess from '@/components/auth/withRoleAccess';
import rolePermissions from '@/utils/rolePermissions';

function SistemMonitoring() {

    const router = useRouter();
    const { user, isAuthenticated, loading: userLoading } = useUser();

    const [moduleName, setModuleName] = useState("");

    useEffect(() => {
        if (userLoading) return;
        if (!isAuthenticated()) {
            router.push("/login");
            return;
        }
        // if (!user?.menu_link_code?.some((item) => item.includes("KodeProduk/masterdata"))) {
        //     router.push("/dashboard");
        //     return;
        // }
        setModuleName(user.module_name || "");
    }, [userLoading]);

    return (
        <div>
            <div>
                <Head>
                    <title>IoT Availability Monitoring</title>
                </Head>
            </div>
            <ContainerLayout title="User Module">


                <iframe
                    src="https://metabase.sakafarma.com/public/dashboard/8477e760-78fc-4a5e-b7f0-8e5c443aa873"
                    title="Metabase Dashboard"
                    width="100%"
                    height="800px"
                ></iframe>


            </ContainerLayout>
        </div>
    );
}

export default withRoleAccess(SistemMonitoring, rolePermissions['sisko/masterdata/kode_produk']);