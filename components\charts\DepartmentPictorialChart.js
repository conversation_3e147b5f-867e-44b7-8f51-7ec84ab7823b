// components/charts/DepartmentPictorialChart.js

import React from 'react';
import ReactECharts from 'echarts-for-react';

const pathSymbols = {
  gear: 'path://M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.44,0.17-0.48,0.41L9.18,5.05C8.59,5.29,8.06,5.61,7.56,5.99L5.17,5.03C4.95,4.94,4.7,5.01,4.58,5.23l-1.92,3.32 c-0.12,0.22-0.07,0.47,0.12,0.61l2.03,1.58c-0.05,0.3-0.09,0.62-0.09,0.94s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.42,2.24 c0.04,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.48-0.41l0.42-2.24c0.59-0.24,1.12-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0.01,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z',
};

const DepartmentPictorialChart = ({ chartData }) => {
  if (!chartData) {
    return null;
  }

  const departmentLabels = chartData.labels;
  const projectCounts = chartData.datasets[0].data;

  const glyphData = projectCounts.map(count => ({
    value: count,
    symbol: pathSymbols.gear,
    symbolSize: [10, 10]
  }));

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'none' },
      formatter: (params) => {
        return `${params[0].name}<br/>Jumlah Proyek: <strong>${params[0].value}</strong>`;
      },
    },
    xAxis: {
      data: departmentLabels,
      axisTick: { show: false },
      axisLine: { show: false },
      axisLabel: {
        color: '#333',
        interval: 0,
        rotate: 30,
      }
    },
    // --- PERUBAHAN UTAMA ADA DI SINI ---
    yAxis: {
      splitLine: { 
        show: true,
        lineStyle: {
            type: 'dashed',
            color: '#eee'
        }
      },
      axisLine: { show: true },
      axisLabel: { show: true }
    },
    // --- AKHIR DARI PERUBAHAN ---
    color: ['#3398DB'],
    series: [
      {
        name: 'hill',
        type: 'pictorialBar',
        barCategoryGap: '-130%',
        symbol: 'path://M0,10 L10,10 C5.5,10 5.5,5 5,0 C4.5,5 4.5,10 0,10 z',
        itemStyle: { opacity: 0.5 },
        emphasis: { itemStyle: { opacity: 1 } },
        data: projectCounts,
        z: 10
      },
      {
        name: 'glyph',
        type: 'pictorialBar',
        barGap: '-100%',
        symbolPosition: 'end',
        symbolSize: 50,
        symbolOffset: [0, '-120%'],
        data: glyphData,
      }
    ],
    grid: {
        left: '3%',
        right: '4%',
        bottom: '15%',
        containLabel: true
    }
  };

  return <ReactECharts option={option} style={{ height: '300px' }} />;
};

export default DepartmentPictorialChart;