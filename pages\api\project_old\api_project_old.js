import createApiFunction from "@/lib/apiClient";

export default function ApiProjectOld() {
    return {
        getAllOldProject: createApiFunction("get", "project_old/progress-summary"),
        getAllOldProjectList: createApiFunction("get", "project_old/list"),
        getOldProjectById: createApiFunction("post", "project_old/id"),
        createOldProject: createApiFunction("post", "project_old/create"),
        updateOldProject: createApiFunction("put", "project_old/edit"),
        updateProjectOldtatus: createApiFunction("put", "project_old/edit-status"),
        updateWithTrail: createApiFunction("put", "project_old/edit-with-audit"),
        updateProgressWithTrail: createApiFunction("put", "project_old/edit-progress-with-audit")
    };
}