import createApiFunction from "@/lib/apiClient";

export default function ApiProjectOld() {
    return {
        getAllOldProject: createApiFunction("get", "project_old/progress-summary"),
        getOldProjectById: createApiFunction("post", "project_old/id"),
        createOldProject: createApiFunction("post", "project_old/create"),
        updateOldProject: createApiFunction("put", "project_old/edit"),
        updateProjectOldtatus: createApiFunction("put", "project_old/edit-status"),
    };
}