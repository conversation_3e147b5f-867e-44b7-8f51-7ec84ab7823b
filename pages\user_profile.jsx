import React, { useState, useEffect } from 'react';
import { Panel, Button, Input, Form, SelectPicker, Uploader, Avatar, Divider, Toggle, Notification } from 'rsuite';
import ContainerLayout from '@/components/layout/ContainerLayout';
import { useUser } from '../context/UserContext';
import ApiMasterdataRoles from '@/pages/api/masterdata_roles/masterdata_roles';
import withRoleAccess from '@/components/auth/withRoleAccess';
import rolePermissions from '@/utils/rolePermissions';
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faUser } from "@fortawesome/free-solid-svg-icons";

const customNavMenu = [
    { name: 'Dashboard', route: 'dashboard', icon: 'faHome' },
    { name: 'User Profile', route: 'user-profile', icon: 'faUser' },
    { name: 'Masterdata roles', route: 'masterdata-roles', icon: 'faUsers' },
];

function UserProfile() {
    const { user, loading } = useUser();
    const [isEditing, setIsEditing] = useState(false);
    const [roleDetails, setRoleDetails] = useState(null);
    const [formData, setFormData] = useState({
        username: user?.username || '',
        email: user?.email || '',
        position: user?.position || 'Software Developer',
        joinDate: user?.joinDate || '2023-01-15',
    });

    useEffect(() => {
        if (user?.role_id) {
            fetchRoleDetails(user.role_id);
        }
    }, [user]);

    const fetchRoleDetails = async (roleId) => {
        try {
            const res = await ApiMasterdataRoles().getRoleById({ id: roleId });

            if (res.status === 200) {
                setRoleDetails(res.data);
                console.log("Role details:", res.data);
            } else {
                console.error("Error fetching role details:", res.message);
            }
        } catch (error) {
            console.error("Error fetching role details:", error);
        }
    };

    if (loading || !user) {
        return (
            <div className="min-h-screen flex items-center justify-center bg-gray-50">
                <div className="flex flex-col items-center space-y-4">
                    <div className="animate-spin rounded-full h-12 w-12 border-4 border-blue-500 border-t-transparent"></div>
                    <p className="text-gray-600 font-medium">Loading your profile...</p>
                </div>
            </div>
        );
    }

    const handleSave = () => {
        Notification.success({
            title: 'Success',
            description: 'Profile updated successfully!'
        });
        setIsEditing(false);
    };

    const profileStats = [
        { label: "Projects", value: "12", icon: "📊", color: "bg-blue-500" },
        { label: "Tasks Done", value: "89", icon: "✅", color: "bg-green-500" },
        { label: "Team Size", value: "8", icon: "👥", color: "bg-purple-500" },
        { label: "Experience", value: "3y", icon: "🏆", color: "bg-orange-500" }
    ];

    const recentActivities = [
        { action: "Updated project documentation", time: "2 hours ago", type: "update" },
        { action: "Completed code review", time: "1 day ago", type: "complete" },
        { action: "Joined new team meeting", time: "2 days ago", type: "join" },
        { action: "Created new repository", time: "1 week ago", type: "create" }
    ];

    return (
        <ContainerLayout customNavMenu={customNavMenu}>
            <div className="space-y-6">
                {/* Profile Header */}
                <div className="bg-gradient-to-r from-green-600 to-emerald-600 rounded-2xl p-8 ml-11 mr-11 text-white shadow-xl">
                    <div className="flex flex-col md:flex-row items-center md:items-start space-y-4 md:space-y-0 md:space-x-6">
                        <div className="relative">
                            <div className="w-24 h-24 rounded-full bg-white flex items-center justify-center border-4 border-white shadow-lg">
                                <FontAwesomeIcon
                                    icon={faUser}
                                    className="text-green-600"
                                    style={{ fontSize: '3rem' }}
                                />
                            </div>
                        </div>
                        <div className="flex-1 text-center md:text-left">
                            <h1 className="text-3xl font-bold mb-2">{formData.username}</h1>
                            <p className="text-indigo-100 text-lg mb-1">{roleDetails?.role_name || 'Loading...'}</p>
                            <p className="text-indigo-200">{formData.department}</p>
                            <div className="flex flex-wrap justify-center md:justify-start gap-2 mt-4">
                                <span className="bg-white bg-opacity-20 px-3 py-1 rounded-full text-sm">
                                    📧 {user.email}
                                </span>

                            </div>
                        </div>

                    </div>
                </div>


                {/* Main Content - User Information Only */}
                <div className="max-w-4xl mx-auto">
                    <div className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
                        <div className="bg-gray-50 px-6 py-4 border-b border-gray-200">
                            <h3 className="text-lg font-semibold text-gray-800 flex items-center">
                                <span className="mr-2">👤</span>
                                User Information
                            </h3>
                        </div>
                        <div className="p-8">
                            <Form fluid>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-2">Username</label>
                                        <Input
                                            value={user.username}
                                            disabled={!isEditing}
                                            onChange={(value) => setFormData({ ...formData, username: value })}
                                            className="w-full"
                                            size="lg"
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-2">Employee ID</label>
                                        <Input
                                            value={user.no_karyawan}
                                            disabled={true}
                                            className="w-full bg-gray-50"
                                            size="lg"
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-2">Email</label>
                                        <Input
                                            value={user.email}
                                            disabled={!isEditing}
                                            onChange={(value) => setFormData({ ...formData, email: value })}
                                            className="w-full"
                                            size="lg"
                                        />
                                    </div>


                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-2">Role ID</label>
                                        <Input
                                            value={user.role_id}
                                            disabled={true}
                                            className="w-full bg-gray-50"
                                            size="lg"
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-2">Role Name</label>
                                        <Input
                                            value={roleDetails?.role_name || 'Loading...'}
                                            disabled={true}
                                            className="w-full bg-gray-50"
                                            size="lg"
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-2">Deskripsi</label>
                                        <Input
                                            value={roleDetails?.description || 'Loading...'}
                                            disabled={true}
                                            className="w-full bg-gray-50"

                                            size="lg"
                                        />
                                    </div>

                                </div>

                                {isEditing && (
                                    <div className="mt-8 flex justify-end space-x-3">
                                        <Button
                                            appearance="subtle"
                                            size="lg"
                                            onClick={() => setIsEditing(false)}
                                        >
                                            Cancel
                                        </Button>
                                        <Button
                                            appearance="primary"
                                            size="lg"
                                            onClick={handleSave}
                                        >
                                            💾 Save Changes
                                        </Button>
                                    </div>
                                )}
                            </Form>
                        </div>
                    </div>
                </div>
            </div>
        </ContainerLayout>
    );
}

export default withRoleAccess(UserProfile, rolePermissions['user_profile']);
