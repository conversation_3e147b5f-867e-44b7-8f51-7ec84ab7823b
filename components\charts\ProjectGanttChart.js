import React from 'react';
import ReactECharts from 'echarts-for-react';

// ====================================================================
// SALIN DAN TEMPELKAN (PASTE) HASIL JSON DARI BLOK SEBELUMNYA DI SINI
const ganttData = {
  "project_names": [
    "Penambahan kolom Ori Price, Ori Currency, Rate, dan Rate Date pada Purchase Monitoring Report serta review line PO yang terjadi duplikasi",
    "Personalisasi Booked Order OM",
    "Perubahan Report Stock Card",
    "K-Partner",
    "Validation Cabang bisa dibuat Otomatis",
    "Report XSFL INV Monthly Inventory Mutation",
    "Request New Report Bukti Potong (UP)",
    "(UN PLAN) Custom COA Phase 3"
  ],
  "chart_data": [
    { "name": "Estimasi", "value": [0, 1704067200000, 1711843200000, "Penambahan kolom Ori Price..."], "itemStyle": { "normal": { "color": "#a8d8ea" } } },
    { "name": "Aktual", "value": [0, 1704067200000, 1753946888178, "Progress: 60%"], "itemStyle": { "normal": { "color": "#f9a825" } } },
    { "name": "Estimasi", "value": [1, 1714521600000, 1722384000000, "Personalisasi Booked Order OM"], "itemStyle": { "normal": { "color": "#a8d8ea" } } },
    { "name": "Aktual", "value": [1, 1714521600000, 1753946888178, "Progress: 50%"], "itemStyle": { "normal": { "color": "#f9a825" } } },
    { "name": "Estimasi", "value": [2, 1719792000000, 1727654400000, "Perubahan Report Stock Card"], "itemStyle": { "normal": { "color": "#a8d8ea" } } },
    { "name": "Aktual", "value": [2, 1714521600000, 1753946888178, "Progress: 10%"], "itemStyle": { "normal": { "color": "#f9a825" } } },
    { "name": "Estimasi", "value": [3, 1704067200000, 1735603200000, "K-Partner"], "itemStyle": { "normal": { "color": "#a8d8ea" } } },
    { "name": "Aktual", "value": [3, 1704067200000, 1753946888178, "Progress: 60%"], "itemStyle": { "normal": { "color": "#f9a825" } } },
    { "name": "Estimasi", "value": [4, 1714521600000, 1735603200000, "Validation Cabang bisa dibuat Otomatis"], "itemStyle": { "normal": { "color": "#a8d8ea" } } },
    { "name": "Aktual", "value": [4, 1710720000000, 1753946888178, "Progress: 80%"], "itemStyle": { "normal": { "color": "#f9a825" } } },
    { "name": "Estimasi", "value": [5, 1717200000000, 1725062400000, "Report XSFL INV Monthly Inventory Mutation"], "itemStyle": { "normal": { "color": "#a8d8ea" } } },
    { "name": "Aktual", "value": [5, 1714521600000, 1717372800000, "Progress: 100%"], "itemStyle": { "normal": { "color": "#54b345" } } },
    { "name": "Estimasi", "value": [6, 1716163200000, 1717200000000, "Request New Report Bukti Potong (UP)"], "itemStyle": { "normal": { "color": "#a8d8ea" } } },
    { "name": "Aktual", "value": [6, 1716163200000, 1717372800000, "Progress: 100%"], "itemStyle": { "normal": { "color": "#f44336" } } },
    { "name": "Estimasi", "value": [7, 1714521600000, 1716595200000, "(UN PLAN) Custom COA Phase 3"], "itemStyle": { "normal": { "color": "#a8d8ea" } } },
    { "name": "Aktual", "value": [7, 1714521600000, 1716595200000, "Progress: 100%"], "itemStyle": { "normal": { "color": "#54b345" } } }
  ]
};
// ====================================================================


const ProjectGanttChart = () => {
  const option = {
    tooltip: {
      formatter: (params) => {
        // value[3] berisi nama proyek atau progress
        // value[1] adalah start date, value[2] adalah end date
        const title = ganttData.project_names[params.value[0]];
        const details = params.value[3];
        const startDate = new Date(params.value[1]).toLocaleDateString("id-ID");
        const endDate = new Date(params.value[2]).toLocaleDateString("id-ID");
        return `${title}<br/><strong>${params.name}</strong>: ${startDate} - ${endDate}<br/><i>${details}</i>`;
      }
    },
    legend: {
        data: ['Estimasi', 'Aktual'],
        bottom: 10,
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'time',
      axisLabel: {
        formatter: '{yyyy}-{MM}-{dd}',
      },
    },
    yAxis: {
      type: 'category',
      data: ganttData.project_names,
      axisLabel: {
         // Agar nama proyek yang panjang tidak terpotong
         overflow: 'truncate',
         width: 150
      }
    },
    series: [
      {
        type: 'custom',
        renderItem: (params, api) => {
          const categoryIndex = api.value(0);
          const start = api.coord([api.value(1), categoryIndex]);
          const end = api.coord([api.value(2), categoryIndex]);
          const height = api.size([0, 1])[1] * 0.4; // Tinggi bar 40% dari space

          const rectShape = {
            x: start[0],
            y: start[1] - height,
            width: end[0] - start[0],
            height: height
          };

          return {
            type: 'rect',
            shape: rectShape,
            style: api.style()
          };
        },
        encode: {
          x: [1, 2], // map value[1] dan value[2] ke xAxis
          y: 0,      // map value[0] ke yAxis
        },
        data: ganttData.chart_data
      }
    ]
  };

  return (
    <div className="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
       <h3 className="text-lg font-semibold text-gray-800 mb-4">Timeline Proyek (Estimasi vs. Aktual)</h3>
       <ReactECharts option={option} style={{ height: '400px' }} />
    </div>
  );
};

export default ProjectGanttChart;