import { useRouter } from 'next/router';
import { useEffect } from 'react';
import { useUser } from '@/context/UserContext';
import { useToaster } from 'rsuite';

/**
 * Higher Order Component to restrict page access based on user roles
 * @param {React.Component} Component - The component to wrap
 * @param {Array} allowedRoles - Array of role IDs allowed to access the page
 * @returns {React.Component} - The wrapped component with role-based access control
 */
const withRoleAccess = (Component, allowedRoles = []) => {
  const WithRoleAccess = (props) => {
    const router = useRouter();
    const { user, isAuthenticated, loading } = useUser();
    const toaster = useToaster();

    useEffect(() => {
      // Wait until user data is loaded
      if (loading) return;
      
      // Check if user is authenticated
      if (!isAuthenticated()) {
        router.push("/login");
        return;
      }
      
      // Check if user has the required role
      if (!allowedRoles.includes(user.role_id)) {
        toaster.push({
          message: "Anda tidak memiliki izin mengakses halaman ini",
          type: "error",
        });
        router.push("/dashboard");
        return;
      }
    }, [user, loading]);

    // If still loading or not authenticated, show nothing
    if (loading || !isAuthenticated()) {
      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
          <div className="flex flex-col items-center space-y-4">
            <div className="animate-spin rounded-full h-12 w-12 border-4 border-blue-500 border-t-transparent"></div>
            <p className="text-gray-600 font-medium">Loading...</p>
          </div>
        </div>
      );
    }

    // If user doesn't have the required role, don't render anything
    // (the useEffect will redirect them)
    if (!allowedRoles.includes(user.role_id)) {
      return null;
    }

    // If user has the required role, render the component
    return <Component {...props} />;
  };

  return WithRoleAccess;
};

export default withRoleAccess;