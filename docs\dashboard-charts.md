# Dashboard Charts Documentation

## Overview
Dashboard ini telah dilengkapi dengan berbagai chart informatif menggunakan Chart.js dan react-chartjs-2 untuk memberikan visualisasi data yang komprehensif.

## Chart yang Tersedia

### 1. Statistics Cards
Menampilkan ringkasan statistik utama:
- **Total PPR Disetujui**: 123 (+12% dari bulan lalu)
- **Transaksi Header**: 511 (+8% dari bulan lalu)  
- **Skor Kualitas Rata-rata**: 92% (+3% dari bulan lalu)
- **Produk Aktif**: 87 (+5 produk baru)

### 2. PPR yang Disetujui (Bar Chart)
- **Tipe**: Bar Chart
- **Data**: Jumlah PPR yang disetujui per bulan (Jan-Jun)
- **Warna**: <PERSON><PERSON><PERSON> (rgba(34, 197, 94))
- **Lokasi**: <PERSON><PERSON> pertam<PERSON>, kolom kiri

### 3. Transaksi Header Disetujui (Line Chart)
- **Tipe**: Line Chart dengan area fill
- **Data**: Jumlah transaksi header yang disetujui per bulan
- **Warna**: <PERSON>ir<PERSON> (rgba(59, 130, 246))
- **Lokasi**: Baris pertama, kolom kanan

### 4. Status Distribusi Produk (Doughnut Chart)
- **Tipe**: Doughnut Chart
- **Data**: 
  - Tersedia: 45%
  - Dalam Proses: 25%
  - Habis: 15%
  - Pending: 15%
- **Lokasi**: Baris kedua, kolom kiri

### 5. Trend Kualitas Bulanan (Line Chart)
- **Tipe**: Line Chart dengan area fill
- **Data**: Skor kualitas dalam persentase per bulan
- **Warna**: Ungu (rgba(168, 85, 247))
- **Lokasi**: Baris kedua, kolom kanan

### 6. Top 5 Produk Terlaris (Horizontal Bar Chart)
- **Tipe**: Horizontal Bar Chart
- **Data**: 
  - Paracetamol 500mg: 320
  - Amoxicillin 250mg: 280
  - Vitamin C 1000mg: 250
  - Ibuprofen 400mg: 220
  - Aspirin 100mg: 180
- **Lokasi**: Baris ketiga, full width

### 7. Performa Sistem (Doughnut Chart)
- **Tipe**: Doughnut Chart
- **Data**:
  - CPU Usage: 45%
  - Memory Usage: 62%
  - Disk Usage: 38%
  - Network Usage: 25%
- **Lokasi**: Baris keempat, kolom kiri

### 8. Perbandingan Status PPR Bulanan (Multi-line Chart)
- **Tipe**: Multi-line Chart
- **Data**:
  - PPR Disetujui (hijau)
  - PPR Ditolak (merah)
  - PPR Pending (kuning)
- **Lokasi**: Baris keempat, kolom kanan

### 9. Performa Departemen (Radar Chart)
- **Tipe**: Radar Chart
- **Data**:
  - Quality Control: 85
  - Production: 92
  - R&D: 78
  - Regulatory: 88
  - Supply Chain: 90
  - Marketing: 82
- **Lokasi**: Baris kelima, full width

## Teknologi yang Digunakan

### Dependencies
- `chart.js`: ^4.5.0
- `react-chartjs-2`: ^5.3.0

### Chart.js Components Registered
- CategoryScale
- LinearScale
- BarElement
- LineElement
- PointElement
- ArcElement
- Title
- Tooltip
- Legend
- Filler

## Kustomisasi Data

### Untuk Backend Integration
Semua data chart saat ini menggunakan dummy data. Untuk integrasi dengan backend:

1. **Buat API endpoints** untuk setiap chart data
2. **Update state management** untuk fetch data dari API
3. **Tambahkan loading states** untuk setiap chart
4. **Handle error states** jika API gagal

### Contoh API Endpoints yang Dibutuhkan
```javascript
// API endpoints yang perlu dibuat di backend
/api/dashboard/ppr-approved        // PPR yang disetujui
/api/dashboard/transaction-header  // Transaksi header
/api/dashboard/product-distribution // Status distribusi
/api/dashboard/quality-trend       // Trend kualitas
/api/dashboard/top-products        // Produk terlaris
/api/dashboard/system-performance  // Performa sistem
/api/dashboard/ppr-comparison      // Perbandingan PPR
/api/dashboard/department-performance // Performa departemen
```

### Struktur Data yang Diharapkan
```javascript
// Contoh struktur response untuk PPR approved
{
  "status": "success",
  "data": {
    "labels": ["Jan", "Feb", "Mar", "Apr", "May", "Jun"],
    "values": [12, 19, 15, 25, 22, 30]
  }
}
```

## Styling dan Responsiveness
- Semua chart menggunakan `responsive: true`
- `maintainAspectRatio: false` untuk kontrol ukuran yang lebih baik
- Grid layout responsif dengan Tailwind CSS
- Tinggi chart disesuaikan per jenis (300px untuk kebanyakan, 400px untuk radar)

## Future Enhancements
1. **Real-time updates** dengan WebSocket
2. **Export functionality** (PDF, PNG, Excel)
3. **Date range filters** untuk semua chart
4. **Drill-down capabilities** untuk detail data
5. **Custom color themes** berdasarkan user preference
6. **Animation improvements** untuk transisi data
7. **Tooltip customization** dengan informasi lebih detail
